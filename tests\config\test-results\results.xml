<testsuites id="" name="" tests="5" failures="2" skipped="0" errors="0" time="34.261089">
<testsuite name="user-journeys\company-onboarding.spec.js" timestamp="2025-08-18T12:41:32.636Z" hostname="chromium" tests="5" failures="2" skipped="0" time="49.606" errors="0">
<testcase name="@user-journey @critical User Journey - Company Onboarding › should navigate from pre-registration to company registration @regression" classname="user-journeys\company-onboarding.spec.js" time="31.035">
<failure message="company-onboarding.spec.js:56:3 should navigate from pre-registration to company registration @regression" type="FAILURE">
<![CDATA[  [chromium] › user-journeys\company-onboarding.spec.js:56:3 › @user-journey @critical User Journey - Company Onboarding › should navigate from pre-registration to company registration @regression 

    Test timeout of 30000ms exceeded.

    Error: locator.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for getByR<PERSON>('button', { name: 'Create Account' })
        - locator resolved to <button disabled data-cy="register-button" class="flex items-center h-[2.625rem] justify-center gap-1 px-4 py-3 font-medium rounded-xl focus:outline-none focus:ring transition ease-in-out cursor-pointer text-white bg-[#BBBCBE] hover:bg-gray3 hover:cursor-not-allowed w-full">…</button>
      - attempting click action
        2 × waiting for element to be visible, enabled and stable
          - element is not enabled
        - retrying click action
        - waiting 20ms
        2 × waiting for element to be visible, enabled and stable
          - element is not enabled
        - retrying click action
          - waiting 100ms
        51 × waiting for element to be visible, enabled and stable
           - element is not enabled
         - retrying click action
           - waiting 500ms


      67 |
      68 |     const registerButton = page.getByRole('button', { name: 'Create Account' })
    > 69 |     await registerButton.click()
         |                          ^
      70 |
      71 |     await expect(page).toHaveURL('/registration/company-registration')
      72 |     await expect(page.getByRole('heading', { name: 'Join as a Tech Company' })).toBeVisible()
        at C:\Users\<USER>\Documents\github\frontend\tests\e2e\user-journeys\company-onboarding.spec.js:69:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\user-journeys-company-onbo-3e353-any-registration-regression-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\user-journeys-company-onbo-3e353-any-registration-regression-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\user-journeys-company-onbo-3e353-any-registration-regression-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\..\..\test-results\user-journeys-company-onbo-3e353-any-registration-regression-chromium\test-failed-1.png]]

[[ATTACHMENT|..\..\..\test-results\user-journeys-company-onbo-3e353-any-registration-regression-chromium\video.webm]]

[[ATTACHMENT|..\..\..\test-results\user-journeys-company-onbo-3e353-any-registration-regression-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="@user-journey @critical User Journey - Company Onboarding › should display company registration form correctly" classname="user-journeys\company-onboarding.spec.js" time="3.31">
</testcase>
<testcase name="@user-journey @critical User Journey - Company Onboarding › should handle form validation - button disabled initially" classname="user-journeys\company-onboarding.spec.js" time="3.297">
</testcase>
<testcase name="@user-journey @critical User Journey - Company Onboarding › should handle API errors gracefully" classname="user-journeys\company-onboarding.spec.js" time="3.308">
</testcase>
<testcase name="@user-journey @critical User Journey - Company Onboarding › should fill form fields and verify they accept input" classname="user-journeys\company-onboarding.spec.js" time="8.656">
<failure message="company-onboarding.spec.js:132:3 should fill form fields and verify they accept input" type="FAILURE">
<![CDATA[  [chromium] › user-journeys\company-onboarding.spec.js:132:3 › @user-journey @critical User Journey - Company Onboarding › should fill form fields and verify they accept input 

    Error: Timed out 5000ms waiting for expect(locator).toBeChecked()

    Locator: locator('input[id="terms-checkbox"]')
    Expected: checked
    Received: unchecked
    Call log:
      - Expect "toBeChecked" with timeout 5000ms
      - waiting for locator('input[id="terms-checkbox"]')
        9 × locator resolved to <input data-cy="" class="hidden" type="checkbox" id="terms-checkbox"/>
          - unexpected value "unchecked"


      157 |     // Verify checkboxes are checked
      158 |     await expect(page.locator('input[id="company-checkbox"]')).toBeChecked()
    > 159 |     await expect(page.locator('input[id="terms-checkbox"]')).toBeChecked()
          |                                                              ^
      160 |   })
      161 | })
      162 |
        at C:\Users\<USER>\Documents\github\frontend\tests\e2e\user-journeys\company-onboarding.spec.js:159:62

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\test-failed-1.png]]

[[ATTACHMENT|..\..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\test-failed-2.png]]

[[ATTACHMENT|..\..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\video-1.webm]]

[[ATTACHMENT|..\..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\video.webm]]

[[ATTACHMENT|..\..\..\test-results\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>