package com.skilled.SkilledUp;
import java.time.Duration;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import junit.framework.Assert;

public class BasicProfileInfo {
	WebDriver driver;
    
	@BeforeTest
	
	public void LoginBasicUI() throws InterruptedException {
		driver=new ChromeDriver();
		MotivationUI.LoginUI();
		// TODO Auto-generated method stub
		/*
		 * driver=new ChromeDriver();
		 * driver.get("https://v2.skilledup.life/volunteer/profile-creation");
		 */
	}
	@Test
	public void Basic() {
		// TODO Auto-generated method stub
		//Basic profile label
		driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/h2")).isDisplayed());
		
		//Country Code
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[1]/div[1]/div[1]/div/div/input")).isDisplayed());
		//Mobile No
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"phone\"]")).isDisplayed());
		
		//City
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"city\"]")).isDisplayed());
		//Your languages
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[2]/div/div")).isDisplayed());
				
		//Add maximum of 3 tags (Optional)
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[3]/div[2]")).isDisplayed());
		
		//Add your profile picture (Optional)
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[4]/div[2]/label")).isDisplayed());
		
		//Add a video pitch (Optional)
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"video\"]")).isDisplayed());
		//Add social media links (Optional)
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[6]/div/div[1]/div/input")).isDisplayed());
		//url
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"url\"]")).isDisplayed());
		//Add 
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[6]/div/div[3]/button")).isDisplayed());
				
		//Save and COntinue
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[3]/button")).isDisplayed());
		//Back
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[3]/div/button")).isDisplayed());
		
		// Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[1]/div/input")).isDisplayed());
		 
		    //Years of work experience drop dpwn
	
	}
	 //Log out 
	/*@AfterTest
    public void Logout(){
    	Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[1]/div/div[2]/div/div[3]/div/div[4]")).isDisplayed());
    	System.out.println("Logout");*/
    	
    }
		 

