package com.skilled.SkilledUp;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;

import junit.framework.Assert;

public class ProfessionalInfo {
	WebDriver driver;
	public void Edudetails()
	{
		driver=new ChromeDriver();
		//Add Education details label
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/h2")).isDisplayed());
		//Add new 
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/button")).isDisplayed());
		WebElement addEdu=driver.findElement(By.xpath("//*[@id=\\\"app\\\"]/div/div[2]/div/div/div[2]/div[2]/div/button"));
		addEdu.click();
		//pop up 
		
		//Institution Name
	   Assert.assertTrue
		
		
		
	}

}
