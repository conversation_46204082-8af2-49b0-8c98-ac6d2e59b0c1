// 🧪 Unit Tests: Registration Components
// Tests the registration form validation and functionality

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import PreRegistration from '@/pages/registration/PreRegistration.vue'

// Mock API calls
vi.mock('@/api/AuthApi', () => ({
  getCountries: vi.fn(),
  getVolunteersCategories: vi.fn(),
  getCompanyCategories: vi.fn(),
  sendRegistrationRequest: vi.fn()
}))

// Mock stores
vi.mock('@/store/volunteerStore', () => ({
  useVolunteerStore: () => ({
    username: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    selectedCountry: null,
    selectedCategory: null,
    isTerms: false,
    isVolunteer: false,
    resetForm: vi.fn()
  })
}))

vi.mock('@/store/CompanyStore', () => ({
  useCompanyStore: () => ({
    username: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    companyName: '',
    selectedCountry: null,
    selectedCategory: null,
    isTerms: false,
    isCompany: false,
    resetForm: vi.fn()
  })
}))

// Mock router
const mockRouter = {
  push: vi.fn()
}

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')
  return {
    ...actual,
    useRouter: () => mockRouter
  }
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// Mock window.open
Object.defineProperty(window, 'open', {
  value: vi.fn()
})

describe('Registration Components', () => {
  let pinia
  let router

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/volunteer-registration', name: 'volunteer-registration' },
        { path: '/company-registration', name: 'company-registration' },
        { path: '/verify-email', name: 'verify-email' }
      ]
    })

    // Reset mocks
    vi.clearAllMocks()
    mockRouter.push.mockResolvedValue()
    sessionStorageMock.getItem.mockReturnValue(null)
    sessionStorageMock.setItem.mockReturnValue()
    sessionStorageMock.removeItem.mockReturnValue()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('PreRegistration Component', () => {
    let wrapper

    const createWrapper = () => {
      return mount(PreRegistration, {
        global: {
          plugins: [pinia, router],
          stubs: {
            'VolunteerIcon': true,
            'CompanyIcon': true,
            'Button': true,
            'Link': true
          }
        }
      })
    }

    beforeEach(() => {
      wrapper = createWrapper()
    })

    afterEach(() => {
      if (wrapper) {
        wrapper.unmount()
      }
    })

    describe('Component Rendering', () => {
      it('should render pre-registration form with volunteer and company options', () => {
        expect(wrapper.find('h2').text()).toBe('Join as a Volunteer or Tech Company')
        expect(wrapper.text()).toContain('I am a Volunteer')
        expect(wrapper.text()).toContain("I'm a Tech Company")
        expect(wrapper.findComponent({ name: 'Button' })).toBeTruthy()
      })

      it('should render login link', () => {
        const loginLink = wrapper.findComponent({ name: 'Link' })
        expect(loginLink.props('href')).toBe('/auth/login')
        expect(loginLink.props('linkText')).toBe('Already have an account?')
      })
    })

    describe('User Type Selection', () => {
      it('should disable create account button when no user type is selected', () => {
        const createButton = wrapper.findComponent({ name: 'Button' })
        expect(createButton.props('disabled')).toBe(true)
      })

      it('should render volunteer and company selection cards', () => {
        const cards = wrapper.findAll('.rounded-xl')
        expect(cards.length).toBeGreaterThanOrEqual(2)
      })

      it('should have proper accessibility attributes', () => {
        const cards = wrapper.findAll('.rounded-xl')
        expect(cards.length).toBeGreaterThanOrEqual(2)
        
        // Check that the cards exist and have the expected structure
        cards.forEach(card => {
          expect(card.exists()).toBe(true)
        })
      })
    })
  })

  describe('Registration Form Validation Utilities', () => {
    // Test the validation logic that would be used in the registration forms
    const isValidEmail = (email) => /\S+@\S+\.\S+/.test(email)
    const isPasswordMatch = (password, confirmPassword) => password === confirmPassword
    const isRequired = (value) => {
      if (!value) return false
      return value.toString().trim().length > 0
    }

    describe('Email Validation', () => {
      it('should validate correct email formats', () => {
        expect(isValidEmail('<EMAIL>')).toBe(true)
        expect(isValidEmail('<EMAIL>')).toBe(true)
        expect(isValidEmail('<EMAIL>')).toBe(true)
      })

      it('should reject invalid email formats', () => {
        expect(isValidEmail('invalid-email')).toBe(false)
        expect(isValidEmail('test@')).toBe(false)
        expect(isValidEmail('@example.com')).toBe(false)
        expect(isValidEmail('')).toBe(false)
        expect(isValidEmail('test.example.com')).toBe(false)
      })
    })

    describe('Password Validation', () => {
      it('should validate password confirmation', () => {
        expect(isPasswordMatch('password123', 'password123')).toBe(true)
        expect(isPasswordMatch('securePassword', 'securePassword')).toBe(true)
      })

      it('should reject mismatched passwords', () => {
        expect(isPasswordMatch('password123', 'different')).toBe(false)
        expect(isPasswordMatch('securePassword', '')).toBe(false)
        expect(isPasswordMatch('', 'securePassword')).toBe(false)
      })
    })

    describe('Required Field Validation', () => {
      it('should validate required fields', () => {
        expect(isRequired('username')).toBe(true)
        expect(isRequired('John')).toBe(true)
        expect(isRequired('  trimmed  ')).toBe(true)
      })

      it('should reject empty or whitespace-only fields', () => {
        expect(isRequired('')).toBe(false)
        expect(isRequired('   ')).toBe(false)
        expect(isRequired(null)).toBe(false)
        expect(isRequired(undefined)).toBe(false)
      })
    })

    describe('Registration Data Validation', () => {
      it('should validate complete volunteer registration data', () => {
        const volunteerData = {
          username: 'testuser',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'password123',
          confirmPassword: 'password123',
          selectedCountry: 1,
          selectedCategory: 1,
          isTerms: true,
          isVolunteer: true
        }

        const isValid = 
          isRequired(volunteerData.username) &&
          isRequired(volunteerData.firstName) &&
          isRequired(volunteerData.lastName) &&
          isValidEmail(volunteerData.email) &&
          isRequired(volunteerData.password) &&
          isPasswordMatch(volunteerData.password, volunteerData.confirmPassword) &&
          volunteerData.selectedCountry &&
          volunteerData.selectedCategory &&
          volunteerData.isTerms &&
          volunteerData.isVolunteer

        expect(isValid).toBe(true)
      })

      it('should validate complete company registration data', () => {
        const companyData = {
          username: 'testcompany',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'password123',
          confirmPassword: 'password123',
          companyName: 'Test Company',
          selectedCountry: 1,
          selectedCategory: 1,
          isTerms: true,
          isCompany: true
        }

        const isValid = 
          isRequired(companyData.username) &&
          isRequired(companyData.firstName) &&
          isRequired(companyData.lastName) &&
          isValidEmail(companyData.email) &&
          isRequired(companyData.password) &&
          isPasswordMatch(companyData.password, companyData.confirmPassword) &&
          isRequired(companyData.companyName) &&
          companyData.selectedCountry &&
          companyData.selectedCategory &&
          companyData.isTerms &&
          companyData.isCompany

        expect(isValid).toBe(true)
      })

      it('should reject incomplete registration data', () => {
        const incompleteData = {
          username: 'testuser',
          firstName: '', // Missing
          lastName: 'Doe',
          email: 'invalid-email', // Invalid
          password: 'password123',
          confirmPassword: 'different', // Mismatch
          selectedCountry: null, // Missing
          selectedCategory: 1,
          isTerms: false, // Not accepted
          isVolunteer: true
        }

        const isValid = 
          isRequired(incompleteData.username) &&
          isRequired(incompleteData.firstName) &&
          isRequired(incompleteData.lastName) &&
          isValidEmail(incompleteData.email) &&
          isRequired(incompleteData.password) &&
          isPasswordMatch(incompleteData.password, incompleteData.confirmPassword) &&
          incompleteData.selectedCountry &&
          incompleteData.selectedCategory &&
          incompleteData.isTerms &&
          incompleteData.isVolunteer

        expect(isValid).toBe(false)
      })
    })
  })

  describe('Session Storage Integration', () => {
    it('should handle session storage operations for registration data', () => {
      const registrationData = {
        username: 'testuser',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      }

      // Test setting data
      sessionStorageMock.setItem('registrationData', JSON.stringify(registrationData))
      expect(sessionStorageMock.setItem).toHaveBeenCalledWith('registrationData', JSON.stringify(registrationData))

      // Test getting data
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(registrationData))
      const savedData = sessionStorageMock.getItem('registrationData')
      expect(savedData).toBe(JSON.stringify(registrationData))

      // Test removing data
      sessionStorageMock.removeItem('registrationData')
      expect(sessionStorageMock.removeItem).toHaveBeenCalledWith('registrationData')
    })

    it('should handle different storage keys for different user types', () => {
      // Volunteer registration data
      sessionStorageMock.setItem('volunteerregistrationData', 'volunteer-data')
      expect(sessionStorageMock.setItem).toHaveBeenCalledWith('volunteerregistrationData', 'volunteer-data')

      // Company registration data
      sessionStorageMock.setItem('companyregistrationData', 'company-data')
      expect(sessionStorageMock.setItem).toHaveBeenCalledWith('companyregistrationData', 'company-data')
    })
  })
})
