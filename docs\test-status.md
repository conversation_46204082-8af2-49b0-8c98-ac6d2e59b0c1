# Test Status Dashboard

Purpose
- Give contributors a quick snapshot of test coverage and stability
- Highlight what’s done, what’s missing, and what’s flaky

<PERSON>napshot (14-08-2025)

Coverage Summary
- Unit/Component: ✅ Stable (key suites green)
- E2E Critical: ✅ Login, ✅ Forgot Password
- E2E Regression: ✅ Seeded via tags; nightly job enabled
- Integration: ⏳ Placeholders added (*.blank)

Implemented Tests
- Unit & Component
  - tests/unit/Login.spec.js
  - tests/unit/Register.spec.js
  - tests/components/Button.spec.js, CheckBox.spec.js, InputField.spec.js, ForgotPassword.spec.js
- E2E
  - tests/e2e/critical/login.spec.js (@critical, + @regression subset)
  - tests/e2e/forgot-password.spec.js (@critical @regression)
  - tests/e2e/user-journeys/company-onboarding.spec.js (@user-journey @critical @regression)
  - tests/e2e/user-journeys/volunteer-onboarding.spec.js (@user-journey @critical @regression)
- Integration (scaffolded; not running)
  - tests/integration/auth-login.integration.spec.js.blank
  - tests/integration/registration-volunteer.integration.spec.js.blank
  - tests/integration/company-profile.integration.spec.js.blank

Critical Flows – Status
| Flow | Status | Notes |
|------|--------|-------|
| Login (all roles, redirects, errors) | ✅ Done | Mocked /login and /search; stable selectors |
| Forgot Password | ✅ Done | Validates, submits, confirms |
| Company Onboarding | ✅ Done | Complete E2E journey with validation & error handling |
| Volunteer Onboarding | ✅ Done | Complete E2E journey with nested steps & validation |
| Profile Update Persistence | 🔴 Missing | Add regression |
| Role Switching | 🔴 Missing | Add user-journey test |
| Notification Settings | 🔴 Missing | Add regression |
| Session Expiry | 🔴 Missing | Add regression |

Skipped/Disabled Tests
- None currently skipped
- Policy: If skipping, document the reason in file and in tracker; prefer tagging (@regression) over skipping where possible

Stability Metrics
- Unit/Component pass rate: 100% (last run)
- E2E pass rate (Chromium local): 100% (Login 13/13, Forgot Password 2/2)
- Flaky tests: None observed after hardening selectors and mocks

CI & Execution
- PR/Push: Lint, Unit/Component, E2E @critical (Chromium-only)
- Nightly: E2E @regression (all browsers)
- Manual: workflow_dispatch with suite = e2e-critical or e2e-regression

Near-Term Improvements
- Add data-cy selectors where role/label are brittle
- Write integration tests by renaming *.blank to *.spec.js and implementing the TODOs
- Add remaining E2E flows (profile update, role switching, notifications, session expiry)
- Consider label-based trigger (full-e2e) later if needed by the team
- ✅ **COMPLETED**: Company & Volunteer onboarding E2E journeys with comprehensive validation
