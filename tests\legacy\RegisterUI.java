package com.skilled.SkilledUp;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.testng.Assert;
import org.testng.annotations.Test;

public class RegisterUI {
	
	@Test
	public void RegUI() {
		WebDriver driver=new ChromeDriver();
	    driver.get("https://v2.skilledup.life/pre-register");
	    //I am a Volunteer
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[1]/div[1]")).isDisplayed());
	    //I am Tech Company 
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[1]/div[2]")).isDisplayed());
	    //Create button 
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/button")).isDisplayed());
	    //Login Button
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div/a")).isDisplayed());
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[3]/div/p")).isDisplayed());
	    System.out.println("False");    
	}

}
