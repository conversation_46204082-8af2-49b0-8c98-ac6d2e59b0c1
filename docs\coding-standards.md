# 📋 Coding Standards - SkilledUp.Life Frontend

> **Purpose**: Enable confident, maintainable code for our distributed volunteer team

---

## 🎯 Guiding Principles

Our coding standards are designed for:
- **Easy adoption** by new volunteers
- **Type safety** and explicit structure
- **Maintainability** across time zones and contributor turnover
- **Reduction of silent bugs** through clear patterns

---

## 🎨 Vue.js Component Standards

### **Component Structure**

```vue
<template>
  <!-- Template content -->
</template>

<script setup lang="ts">
// Imports
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

// Props (always define explicitly)
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  class: {
    type: String,
    default: "",
  },
})

// Emits
const emit = defineEmits<{
  update: [value: string]
  submit: [data: FormData]
}>()

// Reactive data
const isLoading = ref(false)
const formData = ref({
  email: '',
  password: '',
})

// Computed properties
const isFormValid = computed(() => {
  return formData.value.email && formData.value.password
})

// Methods
const handleSubmit = async () => {
  isLoading.value = true
  try {
    // API call logic
    emit('submit', formData.value)
  } catch (error) {
    console.error('Submit failed:', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* Component styles */
</style>
```

### **Props Definition (CRITICAL)**

⚠️ **Always use explicit prop definitions** - this is our most important rule!

#### ✅ **Correct Way**
```javascript
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  class: {
    type: String,
    default: "",
  },
  style: {
    type: [String, Object, Array],
    default: "",
  },
})
```

#### ❌ **Common Bad Practice**
```javascript
// DON'T DO THIS
defineProps(["title", "isActive", "class"])
```

#### **Why Explicit Props Matter**
- **Eliminates Vue warnings** - Prevents console warnings for unrecognized attributes
- **Improves type safety** - Better IDE intellisense and autocompletion
- **Better maintainability** - Future developers know exactly what props expect
- **Prevents silent bugs** - Catches misspelled props and wrong types during development

### **Special Props: `class` and `style`**

Props named `class` and `style` will conflict with native HTML attributes if not defined properly:

```javascript
const props = defineProps({
  class: {
    type: String,
    default: "",
  },
  style: {
    type: [String, Object, Array],
    default: "",
  },
})
```

---

## 🏗️ File Organization

### **Directory Structure**
```
src/
├── components/           # Reusable Vue components
│   ├── ui-kit/          # Base UI components (Button, Input, etc.)
│   ├── auth/            # Authentication components
│   ├── company/         # Company-specific components
│   └── volunteer/       # Volunteer-specific components
├── pages/               # Page components (routes)
├── store/               # Pinia stores
├── api/                 # API service functions
├── utils/               # Utility functions
├── constants/           # Application constants
└── types/               # TypeScript type definitions
```

### **Naming Conventions**

#### **Files**
- **Components**: PascalCase (`UserProfile.vue`, `CompanyCard.vue`)
- **Pages**: PascalCase (`Login.vue`, `CompanyDashboard.vue`)
- **Utilities**: camelCase (`helpers.js`, `validation.js`)
- **Constants**: UPPER_SNAKE_CASE (`API_ENDPOINTS.js`)

#### **Variables & Functions**
- **Variables**: camelCase (`userProfile`, `isLoading`)
- **Functions**: camelCase (`handleSubmit`, `validateEmail`)
- **Constants**: UPPER_SNAKE_CASE (`API_BASE_URL`, `MAX_FILE_SIZE`)

#### **Components**
- **Component names**: PascalCase (`UserProfile`, `CompanyCard`)
- **Props**: camelCase (`userName`, `isActive`)
- **Events**: camelCase (`user-updated`, `form-submitted`)

---

## 🎯 API Integration Standards

### **API Service Pattern**

```javascript
// src/api/user.js
import { API } from './api'

export const getUserProfile = async (userId) => {
  try {
    const response = await API.get(`/users/${userId}`)
    return response.data
  } catch (error) {
    console.error('Failed to fetch user profile:', error)
    throw error
  }
}

export const updateUserProfile = async (userId, userData) => {
  try {
    const response = await API.put(`/users/${userId}`, userData)
    return response.data
  } catch (error) {
    console.error('Failed to update user profile:', error)
    throw error
  }
}
```

### **Error Handling**
- **Always use try/catch** for API calls
- **Log errors** with descriptive messages
- **Re-throw errors** for component-level handling
- **Use consistent error messages** across the application

---

## 🎨 Styling Standards

### **CSS Classes**
- **Use BEM methodology** for complex components
- **Prefer Tailwind classes** for simple styling
- **Use semantic class names** that describe purpose, not appearance

```vue
<template>
  <!-- Good: Semantic class names -->
  <div class="user-profile-card">
    <h2 class="user-profile-card__title">Profile Information</h2>
    <div class="user-profile-card__content">
      <!-- Content -->
    </div>
  </div>
  
  <!-- Good: Tailwind for simple styling -->
  <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
    Submit
  </button>
</template>
```

### **CSS Scoping**
- **Use `<style scoped>`** for component-specific styles
- **Avoid global styles** unless absolutely necessary
- **Use CSS variables** for theme colors and spacing

---

## 🔧 State Management (Pinia)

### **Store Structure**

```javascript
// src/store/user.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getUserProfile, updateUserProfile } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // State
  const user = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const userName = computed(() => user.value?.name || '')

  // Actions
  const fetchUser = async (userId) => {
    isLoading.value = true
    error.value = null
    
    try {
      const userData = await getUserProfile(userId)
      user.value = userData
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateUser = async (userData) => {
    isLoading.value = true
    error.value = null
    
    try {
      const updatedUser = await updateUserProfile(user.value.id, userData)
      user.value = updatedUser
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  return {
    // State
    user,
    isLoading,
    error,
    
    // Getters
    isAuthenticated,
    userName,
    
    // Actions
    fetchUser,
    updateUser,
  }
})
```

### **Store Usage in Components**

```vue
<script setup lang="ts">
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

// Access state
const userName = userStore.userName
const isLoading = userStore.isLoading

// Call actions
const handleUpdateProfile = async (data) => {
  try {
    await userStore.updateUser(data)
    // Handle success
  } catch (error) {
    // Handle error
  }
}
</script>
```

---

## 🚀 Performance Standards

### **Component Optimization**
- **Use `v-memo`** for expensive list rendering
- **Lazy load** heavy components with `defineAsyncComponent`
- **Use `shallowRef`** for large objects that don't need deep reactivity

```vue
<script setup lang="ts">
import { defineAsyncComponent } from 'vue'

// Lazy load heavy components
const HeavyChart = defineAsyncComponent(() => import('./HeavyChart.vue'))
</script>
```

### **Memory Management**
- **Clean up event listeners** in `onUnmounted`
- **Use `watchEffect` cleanup** for reactive subscriptions
- **Avoid memory leaks** in long-lived components

---

## 🧪 Testing Standards

### **Component Testing**
- **Test component behavior**, not implementation details
- **Use `data-cy` attributes** for stable selectors
- **Mock external dependencies** (API, router, store)

```vue
<template>
  <button data-cy="submit-button" @click="handleSubmit">
    Submit
  </button>
</template>
```

```javascript
// Component test
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'

const wrapper = mount(MyComponent, {
  global: {
    plugins: [createTestingPinia()],
  },
})

await wrapper.find('[data-cy="submit-button"]').trigger('click')
expect(wrapper.emitted('submit')).toBeTruthy()
```

---

## 🔍 Code Quality Standards

### **ESLint Rules**
- **No unused variables** or imports
- **Consistent formatting** with Prettier
- **TypeScript strict mode** enabled
- **Vue-specific rules** enforced

### **Pre-commit Hooks**
- **Lint check** on staged files
- **Type checking** for TypeScript files
- **Test run** for changed components

### **Code Review Checklist**
- [ ] Props are explicitly defined with types
- [ ] Error handling is implemented
- [ ] Component is properly tested
- [ ] Code follows naming conventions
- [ ] No console.log statements in production code
- [ ] Performance considerations addressed

---

## 📚 Common Patterns

### **Form Handling**
```vue
<script setup lang="ts">
const formData = ref({
  email: '',
  password: '',
})

const errors = ref({})

const validateForm = () => {
  errors.value = {}
  
  if (!formData.value.email) {
    errors.value.email = 'Email is required'
  }
  
  if (!formData.value.password) {
    errors.value.password = 'Password is required'
  }
  
  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) return
  
  try {
    // Submit form
  } catch (error) {
    // Handle error
  }
}
</script>
```

### **Loading States**
```vue
<template>
  <div>
    <div v-if="isLoading" class="loading-spinner">
      Loading...
    </div>
    <div v-else-if="error" class="error-message">
      {{ error }}
    </div>
    <div v-else>
      <!-- Content -->
    </div>
  </div>
</template>
```

---

## 🚨 Anti-Patterns to Avoid

### **❌ Don't Do This**
```javascript
// Undefined props
defineProps(["title", "isActive"])

// Global state mutations
window.user = userData

// Direct DOM manipulation
document.getElementById('button').click()

// Unhandled promises
fetch('/api/data')

// Console logs in production
console.log('Debug info')
```

### **✅ Do This Instead**
```javascript
// Explicit props
const props = defineProps({
  title: { type: String, required: true },
  isActive: { type: Boolean, default: false },
})

// Store state
const userStore = useUserStore()
userStore.setUser(userData)

// Vue refs
const buttonRef = ref()
buttonRef.value?.click()

// Proper error handling
try {
  const data = await fetch('/api/data')
} catch (error) {
  console.error('API call failed:', error)
}

// Development-only logging
if (import.meta.env.DEV) {
  console.log('Debug info')
}
```

---

## 📖 Resources

- [Vue.js Style Guide](https://vuejs.org/style-guide/)
- [Vue Test Utils](https://test-utils.vuejs.org/)
- [Pinia Documentation](https://pinia.vuejs.org/)
- [TypeScript with Vue](https://vuejs.org/guide/typescript/overview.html)

---

**Last Updated**: 2025-01-20  
**Maintainer**: Frontend Development Team
