// 🧪 Component Tests: Company Profile Creation Components
// Tests the company profile creation components and their functionality

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import Understand from '@/components/company/Understand.vue'
import CompanyDetails from '@/components/company/CompanyDetails.vue'
import BusinessPlan from '@/components/company/BusinessPlan.vue'
import PlanSubscription from '@/components/company/PlanSubscription.vue'
import PaymentMethod from '@/components/company/PaymentMethod.vue'
import PaymentInfo from '@/components/company/PaymentInfo.vue'

// Mock stores
vi.mock('@/store/CompanyProfileCreation', () => ({
  useCompanyProfileCreationStore: () => ({
    company: '',
    team: '',
    funded: '',
    structure: '',
    website: '',
    description: '',
    future_plan_12_18_months: '',
    plan: '',
    price: '',
    duration: '',
    card_name: '',
    credit_card: '',
    expiration_date: '',
    security_code: '',
    resetForm: vi.fn(),
    setField: vi.fn()
  })
}))

// Mock API calls
vi.mock('@/api/mainApi', () => ({
  getCountries: vi.fn(),
  getLanguages: vi.fn(),
  companyProfileStageOne: vi.fn(),
  companyProfileStageTwo: vi.fn()
}))

describe('Company Profile Creation Components', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    vi.clearAllMocks()
  })

  describe('Understand Component', () => {
    it('should render company understanding form correctly', () => {
      const wrapper = mount(Understand)
      
      expect(wrapper.find('h3').text()).toContain('Understanding your Tech Company')
      expect(wrapper.find('textarea').exists()).toBe(true)
      expect(wrapper.find('button').text()).toContain('Next')
    })

    it('should validate company background field', async () => {
      const wrapper = mount(Understand)
      
      const textarea = wrapper.find('textarea')
      const nextButton = wrapper.find('button')
      
      // Initially button should be disabled
      expect(nextButton.attributes('disabled')).toBeDefined()
      
      // Fill company background
      await textarea.setValue('We are a growing tech startup focused on innovative solutions')
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })

    it('should emit validation events', async () => {
      const wrapper = mount(Understand)
      
      const textarea = wrapper.find('textarea')
      await textarea.setValue('Test company background')
      
      // Should emit validation events
      expect(wrapper.emitted('validate')).toBeTruthy()
      expect(wrapper.emitted('validityChange')).toBeTruthy()
    })
  })

  describe('CompanyDetails Component', () => {
    it('should render company details form correctly', () => {
      const wrapper = mount(CompanyDetails)
      
      expect(wrapper.find('h3').text()).toContain('Tech Company details')
      expect(wrapper.find('input[placeholder*="team"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="funding"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="structure"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="website"]').exists()).toBe(true)
    })

    it('should validate required fields', async () => {
      const wrapper = mount(CompanyDetails)
      
      const teamInput = wrapper.find('input[placeholder*="team"]')
      const fundingInput = wrapper.find('input[placeholder*="funding"]')
      const structureInput = wrapper.find('input[placeholder*="structure"]')
      const websiteInput = wrapper.find('input[placeholder*="website"]')
      const nextButton = wrapper.find('button')
      
      // Initially button should be disabled
      expect(nextButton.attributes('disabled')).toBeDefined()
      
      // Fill required fields
      await teamInput.setValue('10-50')
      await fundingInput.setValue('Seed funded')
      await structureInput.setValue('Private Limited')
      await websiteInput.setValue('https://techsolutions.com')
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('BusinessPlan Component', () => {
    it('should render business plan form correctly', () => {
      const wrapper = mount(BusinessPlan)
      
      expect(wrapper.find('h3').text()).toContain('Business plan')
      expect(wrapper.find('textarea[name="description"]').exists()).toBe(true)
      expect(wrapper.find('textarea[name="future_plans"]').exists()).toBe(true)
      expect(wrapper.find('button').text()).toContain('Next')
    })

    it('should validate business plan fields', async () => {
      const wrapper = mount(BusinessPlan)
      
      const descriptionTextarea = wrapper.find('textarea[name="description"]')
      const futurePlansTextarea = wrapper.find('textarea[name="future_plans"]')
      const nextButton = wrapper.find('button')
      
      // Initially button should be disabled
      expect(nextButton.attributes('disabled')).toBeDefined()
      
      // Fill business plan
      await descriptionTextarea.setValue('We build innovative software solutions for modern businesses')
      await futurePlansTextarea.setValue('Expand to international markets and develop AI-powered tools')
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('PlanSubscription Component', () => {
    it('should render plan subscription form correctly', () => {
      const wrapper = mount(PlanSubscription)
      
      expect(wrapper.find('h3').text()).toContain('Plan subscription')
      expect(wrapper.find('input[type="radio"]').exists()).toBe(true)
      expect(wrapper.find('button').text()).toContain('Next')
    })

    it('should select a plan', async () => {
      const wrapper = mount(PlanSubscription)
      
      const basicPlanRadio = wrapper.find('input[value="basic"]')
      const nextButton = wrapper.find('button')
      
      // Initially button should be disabled
      expect(nextButton.attributes('disabled')).toBeDefined()
      
      // Select basic plan
      await basicPlanRadio.setChecked()
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })

    it('should display plan details', () => {
      const wrapper = mount(PlanSubscription)
      
      // Should display plan options
      expect(wrapper.text()).toContain('Basic')
      expect(wrapper.text()).toContain('Pro')
      expect(wrapper.text()).toContain('Enterprise')
    })
  })

  describe('PaymentMethod Component', () => {
    it('should render payment method form correctly', () => {
      const wrapper = mount(PaymentMethod)
      
      expect(wrapper.find('h3').text()).toContain('Payment method')
      expect(wrapper.find('input[placeholder*="card name"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="credit card"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="expiration"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="security"]').exists()).toBe(true)
    })

    it('should validate payment information', async () => {
      const wrapper = mount(PaymentMethod)
      
      const cardNameInput = wrapper.find('input[placeholder*="card name"]')
      const creditCardInput = wrapper.find('input[placeholder*="credit card"]')
      const expirationInput = wrapper.find('input[placeholder*="expiration"]')
      const securityInput = wrapper.find('input[placeholder*="security"]')
      const nextButton = wrapper.find('button')
      
      // Initially button should be disabled
      expect(nextButton.attributes('disabled')).toBeDefined()
      
      // Fill payment info
      await cardNameInput.setValue('Jane Smith')
      await creditCardInput.setValue('****************')
      await expirationInput.setValue('12/25')
      await securityInput.setValue('123')
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })

    it('should validate credit card format', async () => {
      const wrapper = mount(PaymentMethod)
      
      const creditCardInput = wrapper.find('input[placeholder*="credit card"]')
      const nextButton = wrapper.find('button')
      
      // Fill invalid card number
      await creditCardInput.setValue('1234')
      
      // Button should still be disabled
      expect(nextButton.attributes('disabled')).toBeDefined()
      
      // Fill valid card number
      await creditCardInput.setValue('****************')
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('PaymentInfo Component', () => {
    it('should render payment info summary correctly', () => {
      const wrapper = mount(PaymentInfo)
      
      expect(wrapper.find('h3').text()).toContain('Payment information')
      expect(wrapper.find('button').text()).toContain('Next')
    })

    it('should display selected plan information', () => {
      const wrapper = mount(PaymentInfo, {
        props: {
          selectedPlan: {
            name: 'Basic',
            price: '$29/month',
            features: ['Up to 5 volunteers', 'Basic support']
          }
        }
      })
      
      expect(wrapper.text()).toContain('Basic')
      expect(wrapper.text()).toContain('$29/month')
      expect(wrapper.text()).toContain('Up to 5 volunteers')
    })
  })
})
