# Test Case: TC001 - Valid Registration for Volunteer Account

**Module:** Module 2 Volunteer- Registration  
**Author:** QA Team  
**Created Date:** 2025-05-28  
**Priority:** High  
**Precondition:** User is on the "Join as a Volunteer" registration page  
**Test Type:** Functional, Positive  
**Status:** Ready

---

## Objective

To verify that a user can successfully register as a volunteer by entering all valid inputs and accepting the terms and conditions.

---

## Test Steps

| Step No | Action                                                                 | Expected Result                                                    |
|---------|------------------------------------------------------------------------|--------------------------------------------------------------------|


| 1       | Navigate to the "Join as a Volunteer" page                             | The registration form is displayed                                 |
| 2       | Enter a valid username: `volunteerUser01`                              | Username field accepts the input                                   |
| 3       | Enter first name: `steve`                                              | First Name field accepts the input                                 |
| 4       | Enter last name: `john`                                                | Last Name field accepts the input                                  |
| 5       | Enter a valid email: `<EMAIL>`                          | Email field accepts the input                                      |
| 6       | Enter a valid password: `******`                                   | Password field accepts the input                                   |
| 7       | Confirm the password: `******`                                     | Confirm Password field accepts the input                           |
| 8       | Select country: `United Kingdom`                                       | Dropdown value is selected                                         |
| 9       | Select category: `Education`                                           | Dropdown value is selected                                         |
| 10      | Check the box: "I accept there are no financial incentives..."         | Checkbox is checked                                                |
| 11      | Check the box: "I accept the Terms of Service and Privacy Policy"      | Checkbox is checked                                                |
| 12      | Click the "Create an account" button                                   | Account is created successfully; user is redirected or sees a success message |

---

## Test Data

| Field                 | Value                  |
|----------------------|-------------------------|
| Username             | volunteerUser01         |
| First Name           | steve                   |
| Last Name            | john                    |
| Email                | <EMAIL>  |
| Password             | ******                  |
| Confirm Password     | ******                  |
| Country              | United Kingdom          |
| Category             | Education               |

---

## Postconditions

- Verify your email address page
- Verification email sent to user  
- After verification, Welcome to SkilledUp Life page  
- After checking  I confirm that I want to apply to become a volunteer and that I am over 18 years old User 
  can click on Get started to create profile
---

## Note

- Form should prevent submission if any required field is empty or invalid.
