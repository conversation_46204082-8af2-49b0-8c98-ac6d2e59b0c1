// Placeholder: Auth Login Integration Test (does not run until renamed to .spec.js)
// Goal: Mount Login.vue with Pinia and Router, mock useAuthStore.login and /search, assert redirects.
// TODO:
// - setup: mount(Login.vue) with createTestingPinia and a memory router
// - mock: authStore.login → resolve success; API /search → volunteer profile
// - assert: router.push('/volunteer/dashboard') when email_verified=true & profile_step=max
