package com.skilled.SkilledUp;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.testng.annotations.Test;

import junit.framework.Assert;

public class MotivationUI {
	@Test
	public static void LoginUI() throws InterruptedException {
		// TODO Auto-generated method stub
		WebDriver driver=new ChromeDriver();
	    driver.get("https://v2.skilledup.life/volunteer/profile-creation");
	    //Level of higher education drop dpwn
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[1]/div/input")).isDisplayed());
	    //Years of work experience drop dpwn
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[2]/div/input")).isDisplayed());
	    //Motivation for gaining experience and skills through SkilledUp Life volunteering
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[3]/div/input")).isDisplayed());
	    //How did you find us?
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[2]/div/div/div[4]/div/input")).isDisplayed());
	    //Save
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[3]/button")).isDisplayed());
	    Thread.sleep(2000);
	    //clcik on save
	    driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div[2]/div[3]/button")).click();
	    
	}
}
