// @ts-check
import { test, expect } from '@playwright/test'

// All browsers now run in CI; locally only Chromium runs

test.describe('@critical Forgot Password Flow', () => {
  test('opens modal, validates email, submits successfully, and shows confirmation @regression', async ({ page }) => {
    // Navigate to login page
    await page.goto('/auth/login')

    // Open Forgot Password modal
    await page.getByRole('link', { name: 'Forgot password?' }).click()

    // Modal should be visible
    await expect(
      page.getByRole('heading', {
        name: /Enter your email and we'll send you a link to reset your password/i,
      })
    ).toBeVisible()

    // Invalid email should keep submit disabled (component prevents submit when invalid)
    const emailInput = page.getByRole('textbox', { name: 'Email address' })
    await emailInput.fill('invalid-email')
    const submitBtn = page.getByRole('button', { name: /Send link/i })
    await expect(submitBtn).toBeDisabled()

    // Mock API for successful submit
    await page.route('**/forgot-password', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true }),
      })
    })

    // Enter a valid email and submit
    await emailInput.fill('<EMAIL>')
    await expect(submitBtn).toBeEnabled()
    await submitBtn.click()

    // ForgotPassword modal closes and EmailSent modal appears
    await expect(
      page.getByRole('heading', { name: 'Email Sent!' })
    ).toBeVisible({ timeout: 3000 })

    // Note: auto-close is handled by app timer; don't assert hide to avoid flakiness

    // Ensure the original ForgotPassword modal is no longer visible
    await expect(
      page.getByRole('heading', {
        name: /Enter your email and we'll send you a link to reset your password/i,
      })
    ).toBeHidden({ timeout: 1000 })
  })

  test('Cancel closes modal and resets state', async ({ page }) => {
    await page.goto('/auth/login')
    await page.getByRole('link', { name: 'Forgot password?' }).click()

    const emailInput = page.getByRole('textbox', { name: 'Email address' })
    await emailInput.fill('<EMAIL>')

    // Cancel
    await page.getByRole('button', { name: 'Cancel' }).click()

    // Modal closed
    await expect(
      page.getByRole('heading', {
        name: /Enter your email and we'll send you a link to reset your password/i,
      })
    ).toBeHidden({ timeout: 1000 })
  })
})


