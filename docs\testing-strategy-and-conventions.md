# Testing Strategy & Conventions

Purpose
- Make testing predictable, fast, and useful for a distributed volunteer team
- Enable confident merges by automating the right tests at the right times

Guiding Philosophy
- Test behaviour, not implementation details
- Keep PR feedback loops fast (lint, unit/component, critical E2E)
- Run slower/full suites on schedule or when explicitly requested
- Prefer stable selectors (data-cy) and deterministic mocks

Test Types
- Unit: Pure logic, small functions, component methods
- Component: Single Vue component mounted in isolation
- Integration: Component + store + mocked API (no real navigation)
- E2E: Full browser flow (Playwright)
- Regression: Curated set locking down previously working flows

Tooling
- Runner: Vitest (unit/component/integration), Playwright (E2E)
- Vue: Vue Test Utils, JSDOM
- State: Pinia + createTestingPinia for tests
- Node: v20 in CI

Environments & When Tests Run
- Local developer: Fast checks; run Chromium only for E2E
- PR/Push to staging: Lint + Unit/Component + E2E @critical (Chromium)
- Nightly (schedule): E2E @regression across all browsers (Chromium, Firefox, WebKit, Mobile)
- Manual (workflow_dispatch): Choose suite = e2e-critical or e2e-regression

Directory Structure
- tests/unit/ – pure logic and simple component methods
- tests/components/ – isolated component behaviour
- tests/integration/ – component + store + mocked API (use *.integration.spec.js)
- tests/e2e/critical/ – P0 critical flows tagged @critical
- tests/e2e/user-journeys/ – multi-page flows
- tests/e2e/regression/ – curated regression set tagged @regression

Tagging Conventions (Playwright)
- @critical: Must run on every PR (fast subset)
- @regression: Runs nightly and on demand
- Run by tag:
  - npx playwright test --grep @critical
  - npx playwright test --grep @regression

Selectors
- Prefer data-cy over text for brittle elements
- Example: <button data-cy="login">Login</button>

Naming
- kebab-case, clear intent
- feature-or-flow.spec.js or *.integration.spec.js
- One main "happy path" per file; add edge cases nearby

How to Add a New Test (E2E)
- Place critical flows under tests/e2e/critical and tag with @critical
- Place regression-only flows under tests/e2e/regression and tag with @regression
- Use page.route to mock network calls where needed

Quick Commands
- Unit/Component: npm run test:unit
- E2E (local Chromium): npm run test:e2e
- E2E only @critical: npx playwright test --grep @critical
- E2E only @regression: npx playwright test --grep @regression

CI Policy (summary)
- PRs: fast feedback (Chromium-only, @critical)
- Nightly: full cross-browser regression
- Retries enabled in CI to reduce flakes; concurrency cancels duplicate runs

Updating This Document
- Keep in sync with code conventions and CI changes
- Link new tags/selectors and examples as they’re added
