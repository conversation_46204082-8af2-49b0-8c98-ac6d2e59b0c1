package com.skilled.SkilledUp;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.testng.Assert;
import org.testng.annotations.Test;

public class LoginInUI {
	@Test
	public void LoginUI() {
		// TODO Auto-generated method stub
		WebDriver driver=new ChromeDriver();
	    driver.get("//https://v2.skilledup.life/login");
	    //username or Email
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[1]/div[1]/label")).isDisplayed());
	    //Password
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[2]/div[1]/label")).isDisplayed());
	    //Login button
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/button")).isDisplayed());
	    //Forgot password
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[3]/a")).isDisplayed());
	    //Register
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div/div/a")).isDisplayed());
	    
	    
	}
	

}
