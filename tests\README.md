# Testing Strategy - SkilledUp Life Frontend

## App Structure Overview

### Authentication Flow
- **Login with role-based routing**: Users can log in as volunteers, companies, or system admins
- **Email verification**: Unverified users are redirected to email verification
- **Profile completion**: Incomplete profiles are redirected to profile creation
- **Remember me functionality**: Email is saved to localStorage for convenience

### Registration Flow
- **Pre-registration**: Users choose between volunteer or company registration
- **Volunteer registration**: Complete form with personal details, country, category
- **Company registration**: Complete form with company details, country, category
- **Email verification**: Users must verify their email before proceeding
- **Profile creation**: Multi-step process to complete user profiles

### Role-based Dashboards
- **Volunteer Dashboard**: For verified volunteers with complete profiles
- **Company Dashboard**: For verified companies with complete profiles
- **System Admin Dashboard**: For superadmin users

## Test Structure

### Critical Tests (`@critical`)
- Login functionality (all roles)
- Form validation
- Error handling
- Authentication flows

### User Journey Tests (`@user-journey`)
- Complete volunteer onboarding
- Complete company onboarding
- Registration flows
- Profile creation processes

## Running Tests

```bash
# All tests
npm run test

# Unit tests only
npm run test:unit

# E2E tests only
npm run test:e2e

# Critical tests only (fast feedback)
npm run test:e2e:critical

# User journey tests (comprehensive flows)
npm run test:e2e:user-journey
```

## Server Management

Playwright uses **smart server management**:
- **Development**: Uses existing dev server if running, starts fresh if needed
- **CI/CD**: Always starts fresh server for reliability
- **Benefits**: Faster development, consistent environment, automatic port handling

See `tests/config/playwright.config.js` for detailed configuration.

## Test Data Requirements

- Test users for each role (volunteer, company, admin)
- Mock API responses for different scenarios
- Test data for form validation