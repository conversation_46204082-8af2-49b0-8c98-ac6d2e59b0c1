// 🧪 User Journey Test: Complete Volunteer Onboarding Flow
// Tests the actual SkilledUp Life volunteer registration and onboarding process

import { test, expect } from '@playwright/test'

test.describe('@user-journey @critical User Journey - Volunteer Onboarding', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses for the entire flow with correct payload shapes
    await page.route('**/countries', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            { id: 1, name: 'United Kingdom', code: 'GB', phone: '+44' },
            { id: 2, name: 'United States', code: 'US', phone: '+1' }
          ]
        })
      })
    })

    await page.route('**/volunteer-categories', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            { id: 1, name: 'Software Development' },
            { id: 2, name: 'Data Science' }
          ]
        })
      })
    })

    await page.route('**/register', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            token: 'mock-volunteer-token',
            user: {
              id: 1,
              role: ['volunteer'],
              email_verified: false,
              profile_step: 1,
              name: 'testvolunteer'
            }
          }
        })
      })
    })
  })

  test('should navigate from pre-registration to volunteer registration @regression', async ({ page }) => {
    await page.goto('/registration/pre-registration')

    await expect(page.getByRole('heading', { name: 'Join as a Volunteer or Tech Company' })).toBeVisible()
    await expect(page.getByText('I am a Volunteer')).toBeVisible()
    await expect(page.getByText("I'm a Tech Company")).toBeVisible()

    const volunteerCard = page.locator('div').filter({ hasText: 'I am a Volunteer' }).first()
    await volunteerCard.click()

    await page.waitForTimeout(300)

    const registerButton = page.getByRole('button', { name: 'Create Account' })
    await registerButton.click()

    await expect(page).toHaveURL('/registration/volunteer-registration')
    await expect(page.getByRole('heading', { name: 'Join as a Volunteer' })).toBeVisible()
  })

  test('should display volunteer registration form correctly', async ({ page }) => {
    await page.goto('/registration/volunteer-registration')

    await expect(page.getByRole('heading', { name: 'Join as a Volunteer' })).toBeVisible()

    // Verify form fields are present
    await expect(page.locator('input[id="username"]')).toBeVisible()
    await expect(page.locator('input[id="firstname"]')).toBeVisible()
    await expect(page.locator('input[id="lastname"]')).toBeVisible()
    await expect(page.locator('input[id="email"]')).toBeVisible()
    await expect(page.locator('input[id="password"]')).toBeVisible()
    await expect(page.locator('input[id="confirm-password"]')).toBeVisible()

    // Verify dropdowns are present
    await expect(page.locator('input[placeholder="Select your country"]')).toBeVisible()
    await expect(page.locator('input[placeholder="Select category"]')).toBeVisible()

    // Verify checkboxes are present
    await expect(page.locator('label[for="volunteer-checkbox"]')).toBeVisible()
    await expect(page.locator('label[for="example-checkbox"]')).toBeVisible()

    // Verify submit button is present
    await expect(page.getByRole('button', { name: 'Create an account' })).toBeVisible()
  })

  test('should handle form validation - button disabled initially', async ({ page }) => {
    await page.goto('/registration/volunteer-registration')

    const submitButton = page.getByRole('button', { name: 'Create an account' })
    await expect(submitButton).toBeDisabled()
  })

  test('should handle API errors gracefully', async ({ page }) => {
    await page.route('**/register', route => {
      route.fulfill({
        status: 422,
        contentType: 'application/json',
        body: JSON.stringify({
          errors: {
            email: ['This email is already taken'],
            username: ['Username must be unique']
          }
        })
      })
    })

    await page.goto('/registration/volunteer-registration')

    await page.fill('input[id="username"]', 'existinguser')
    await page.fill('input[id="email"]', '<EMAIL>')
    await page.fill('input[id="password"]', 'Password123!')
    await page.fill('input[id="confirm-password"]', 'Password123!')

    await expect(page.getByText('This email is already taken')).not.toBeVisible()
  })

  test('should fill form fields and verify they accept input', async ({ page }) => {
    await page.goto('/registration/volunteer-registration')

    // Fill form fields
    await page.fill('input[id="username"]', 'testvolunteer')
    await page.fill('input[id="firstname"]', 'John')
    await page.fill('input[id="lastname"]', 'Doe')
    await page.fill('input[id="email"]', '<EMAIL>')
    await page.fill('input[id="password"]', 'Password123!')
    await page.fill('input[id="confirm-password"]', 'Password123!')

    // Verify fields contain the input
    await expect(page.locator('input[id="username"]')).toHaveValue('testvolunteer')
    await expect(page.locator('input[id="firstname"]')).toHaveValue('John')
    await expect(page.locator('input[id="lastname"]')).toHaveValue('Doe')
    await expect(page.locator('input[id="email"]')).toHaveValue('<EMAIL>')
    await expect(page.locator('input[id="password"]')).toHaveValue('Password123!')
    await expect(page.locator('input[id="confirm-password"]')).toHaveValue('Password123!')

    // Test checkbox interactions
    await page.click('label[for="volunteer-checkbox"]')
    await page.click('label[for="example-checkbox"]')

    // Verify checkboxes are checked
    await expect(page.locator('input[id="volunteer-checkbox"]')).toBeChecked()
    await expect(page.locator('input[id="example-checkbox"]')).toBeChecked()
  })

  test('should handle form data persistence', async ({ page }) => {
    await page.goto('/registration/pre-registration')
    const volunteerCard = page.locator('div').filter({ hasText: 'I am a Volunteer' }).first()
    await volunteerCard.click()
    await page.waitForTimeout(300)
    await page.click('button:has-text("Create Account")')

    await page.fill('input[id="username"]', 'testvolunteer')
    await page.fill('input[id="firstname"]', 'John')
    await page.fill('input[id="lastname"]', 'Doe')

    await page.goto('/')

    await page.goto('/registration/volunteer-registration')

    await expect(page.locator('input[id="username"]')).toHaveValue('testvolunteer')
    await expect(page.locator('input[id="firstname"]')).toHaveValue('John')
    await expect(page.locator('input[id="lastname"]')).toHaveValue('Doe')
  })
})
