package com.skilled.SkilledUp;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.testng.Assert;
import org.testng.annotations.Test;

public class RegisterVoluteerUI {
	@Test
	public void RegisterVolUI() {
		WebDriver driver=new ChromeDriver();
	    driver.get("https://v2.skilledup.life/volunteer-registration");
		//Join as a Volunteer
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/h2")).isDisplayed());
		//User Name
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[1]/div[1]/label")).isDisplayed());
		
		//First Name
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[2]/div[2]/div/div[1]/label")).isDisplayed());
		//Last Name
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[2]/div[2]/div/div[1]/label")).isDisplayed());
		
		//Email
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[3]/div[1]/label")).isDisplayed());
	
		//Password
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[4]/div[1]/div/div[1]/label")).isDisplayed());
			
		//Confirm Password
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[4]/div[2]/div/div[1]/label")).isDisplayed());
		
	
		//Country
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[5]/div[1]/div/label")).isDisplayed());
	
		//Category
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[5]/div[2]/div/label")).isDisplayed());
				
		//I accept there are no financial incentives associated with volunteering
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[6]/div[1]/label")).isDisplayed());
		
		//I accept the 	Terms of Service and Privacy Policy
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[6]/div[2]/label/span")).isDisplayed());
		//Create button
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/button")).isDisplayed());
		//REgister as tech company 
		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div/div/a")).isDisplayed());
		//False
		Assert.assertFalse(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[3]/div/p")).isDisplayed());
		System.out.println("False");
		
		
		
	}

}
