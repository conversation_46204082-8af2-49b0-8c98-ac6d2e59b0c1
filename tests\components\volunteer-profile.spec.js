// 🧪 Component Tests: Volunteer Profile Creation Components
// Tests the volunteer profile creation components and their functionality

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import Motivation from '@/components/volunteer/Motivation.vue'
import BasicProfile from '@/components/volunteer/BasicProfile.vue'
import ProfessionalInfo from '@/components/volunteer/ProfessionalInfo.vue'
import CareerAspirations from '@/components/volunteer/CareerAspirations.vue'
import AddEducation from '@/components/volunteer/AddEducation.vue'
import AddExperience from '@/components/volunteer/AddExperience.vue'
import AchievedSkills from '@/components/volunteer/AchievedSkills.vue'

// Mock stores
vi.mock('@/store/profileCreationStore', () => ({
  useProfileCreationStore: () => ({
    motivation: '',
    phone: '',
    city: '',
    selectedCountry: null,
    currentRole: '',
    company: '',
    experienceLevel: '',
    aspirations: '',
    education: [],
    experience: [],
    skills: [],
    resetForm: vi.fn(),
    setField: vi.fn()
  })
}))

// Mock API calls
vi.mock('@/api/mainApi', () => ({
  getCountries: vi.fn(),
  getLanguages: vi.fn(),
  getExperienceYears: vi.fn()
}))

describe('Volunteer Profile Creation Components', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    vi.clearAllMocks()
  })

  describe('Motivation Component', () => {
    it('should render motivation form correctly', () => {
      const wrapper = mount(Motivation)
      
      expect(wrapper.find('h3').text()).toContain('Understanding your motivation')
      expect(wrapper.find('textarea').exists()).toBe(true)
      expect(wrapper.find('button').text()).toContain('Next')
    })

    it('should validate motivation field', async () => {
      const wrapper = mount(Motivation)
      
      const textarea = wrapper.find('textarea')
      const nextButton = wrapper.find('button')
      
      // Initially button should be disabled
      expect(nextButton.attributes('disabled')).toBeDefined()
      
      // Fill motivation
      await textarea.setValue('I want to help build amazing tech solutions')
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })

    it('should emit validation events', async () => {
      const wrapper = mount(Motivation)
      
      const textarea = wrapper.find('textarea')
      await textarea.setValue('Test motivation')
      
      // Should emit validation events
      expect(wrapper.emitted('validate')).toBeTruthy()
      expect(wrapper.emitted('validityChange')).toBeTruthy()
    })
  })

  describe('BasicProfile Component', () => {
    it('should render basic profile form correctly', () => {
      const wrapper = mount(BasicProfile)
      
      expect(wrapper.find('h3').text()).toContain('Basic profile information')
      expect(wrapper.find('input[type="tel"]').exists()).toBe(true) // Phone
      expect(wrapper.find('input[placeholder*="city"]').exists()).toBe(true) // City
      expect(wrapper.find('select').exists()).toBe(true) // Country
    })

    it('should validate required fields', async () => {
      const wrapper = mount(BasicProfile)
      
      const phoneInput = wrapper.find('input[type="tel"]')
      const cityInput = wrapper.find('input[placeholder*="city"]')
      const nextButton = wrapper.find('button')
      
      // Initially button should be disabled
      expect(nextButton.attributes('disabled')).toBeDefined()
      
      // Fill required fields
      await phoneInput.setValue('+1234567890')
      await cityInput.setValue('Tech City')
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('ProfessionalInfo Component', () => {
    it('should render professional info form correctly', () => {
      const wrapper = mount(ProfessionalInfo)
      
      expect(wrapper.find('h3').text()).toContain('Professional information')
      expect(wrapper.find('input[placeholder*="role"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="company"]').exists()).toBe(true)
      expect(wrapper.find('select').exists()).toBe(true) // Experience level
    })

    it('should validate professional information', async () => {
      const wrapper = mount(ProfessionalInfo)
      
      const roleInput = wrapper.find('input[placeholder*="role"]')
      const companyInput = wrapper.find('input[placeholder*="company"]')
      const nextButton = wrapper.find('button')
      
      // Fill professional info
      await roleInput.setValue('Software Developer')
      await companyInput.setValue('Tech Corp')
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('CareerAspirations Component', () => {
    it('should render career aspirations form correctly', () => {
      const wrapper = mount(CareerAspirations)
      
      expect(wrapper.find('h3').text()).toContain('Understanding your career aspirations')
      expect(wrapper.find('textarea').exists()).toBe(true)
      expect(wrapper.find('button').text()).toContain('Next')
    })

    it('should validate career aspirations', async () => {
      const wrapper = mount(CareerAspirations)
      
      const textarea = wrapper.find('textarea')
      const nextButton = wrapper.find('button')
      
      // Fill aspirations
      await textarea.setValue('I want to become a senior developer')
      
      // Button should be enabled
      expect(nextButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('AddEducation Component', () => {
    it('should render education form correctly', () => {
      const wrapper = mount(AddEducation)
      
      expect(wrapper.find('h3').text()).toContain('Education')
      expect(wrapper.find('input[placeholder*="institution"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="degree"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="year"]').exists()).toBe(true)
    })

    it('should add education entry', async () => {
      const wrapper = mount(AddEducation)
      
      const institutionInput = wrapper.find('input[placeholder*="institution"]')
      const degreeInput = wrapper.find('input[placeholder*="degree"]')
      const yearInput = wrapper.find('input[placeholder*="year"]')
      const addButton = wrapper.find('button:contains("Add Education")')
      
      // Fill education details
      await institutionInput.setValue('Tech University')
      await degreeInput.setValue('Computer Science')
      await yearInput.setValue('2023')
      
      // Add education
      await addButton.trigger('click')
      
      // Should emit add event
      expect(wrapper.emitted('add-education')).toBeTruthy()
    })
  })

  describe('AddExperience Component', () => {
    it('should render experience form correctly', () => {
      const wrapper = mount(AddExperience)
      
      expect(wrapper.find('h3').text()).toContain('Experience')
      expect(wrapper.find('input[placeholder*="company"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="position"]').exists()).toBe(true)
      expect(wrapper.find('input[placeholder*="duration"]').exists()).toBe(true)
    })

    it('should add experience entry', async () => {
      const wrapper = mount(AddExperience)
      
      const companyInput = wrapper.find('input[placeholder*="company"]')
      const positionInput = wrapper.find('input[placeholder*="position"]')
      const durationInput = wrapper.find('input[placeholder*="duration"]')
      const addButton = wrapper.find('button:contains("Add Experience")')
      
      // Fill experience details
      await companyInput.setValue('Previous Tech')
      await positionInput.setValue('Junior Developer')
      await durationInput.setValue('2 years')
      
      // Add experience
      await addButton.trigger('click')
      
      // Should emit add event
      expect(wrapper.emitted('add-experience')).toBeTruthy()
    })
  })

  describe('AchievedSkills Component', () => {
    it('should render skills form correctly', () => {
      const wrapper = mount(AchievedSkills)
      
      expect(wrapper.find('h3').text()).toContain('Skills')
      expect(wrapper.find('input[placeholder*="skill"]').exists()).toBe(true)
      expect(wrapper.find('button:contains("Add Skill")').exists()).toBe(true)
    })

    it('should add skill', async () => {
      const wrapper = mount(AchievedSkills)
      
      const skillInput = wrapper.find('input[placeholder*="skill"]')
      const addButton = wrapper.find('button:contains("Add Skill")')
      
      // Add skill
      await skillInput.setValue('JavaScript')
      await addButton.trigger('click')
      
      // Should emit add event
      expect(wrapper.emitted('add-skill')).toBeTruthy()
    })

    it('should display added skills', async () => {
      const wrapper = mount(AchievedSkills, {
        props: {
          skills: ['JavaScript', 'Vue.js']
        }
      })
      
      // Should display existing skills
      expect(wrapper.text()).toContain('JavaScript')
      expect(wrapper.text()).toContain('Vue.js')
    })
  })
})
