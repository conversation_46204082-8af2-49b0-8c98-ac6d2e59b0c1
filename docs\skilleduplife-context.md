# 🌍 SkilledUp.Life Context

SkilledUp.Life is a global digital volunteering platform that enables individuals to gain real-life work experience while helping startups grow.

## 🚀 Platform Version 2

We are currently building **Version 2** of our platform:
- **Version 1 (Production)**: www.skilledup.life
- **Version 2 (Development)**: v2.skilledup.life
- **Architecture**: Frontend (Vue.js) + Backend (Laravel) connected via API
- **Repository Structure**:
  - Frontend: https://github.com/skilleduplife/frontend
  - Backend: https://github.com/skilleduplife/backend

## 🎯 Mission

To bridge the gap between education and real-world experience by connecting volunteers with meaningful opportunities while helping startups grow.

## 🧑‍🤝‍🧑 Team Dynamics
- Fully remote and distributed
- Cross-functional collaboration between developers, designers, and QA testers
- Agile-based workflows and sprint goals

## 🕒 Volunteer Structure
- 60 working days per volunteer
- Each day capped at 3 flexible hours, determined by the volunteer
- Volunteers are temporary contributors, often balancing learning and impact

## 🛠️ Engineering Workflow

### 🎯 Current State & Goals

**Current Challenges:**
- No CI/CD pipeline to manage PRs from developers
- No coding standards enforcement - developers code as they prefer
- Lack of automated quality checks

**Implementation Goals:**
1. **Phase 1: Foundation** - ESLint + Prettier for formatting and syntax checks
2. **Phase 2: Testing** - Unit tests, E2E tests, and regression testing
3. **Phase 3: Full CI/CD** - Automated PR management and deployment

**Vision:**
- Streamlined processes that enable sustainability across time zones and contributor turnover
- CI/CD pipelines that enforce consistency, reduce errors, and empower contributors to build confidently 