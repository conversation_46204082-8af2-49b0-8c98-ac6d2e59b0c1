# 🧪 Testing Guide - SkilledUp.Life

> **Purpose**: Make testing predictable, fast, and useful for our distributed volunteer team

## 🎯 Guiding Philosophy

- **Test behaviour, not implementation details**
- **Keep PR feedback loops fast** (lint, unit/component, critical E2E)
- **Run slower/full suites on schedule** or when explicitly requested
- **Prefer stable selectors** (`data-cy`) and deterministic mocks

## 🛠️ Tooling & Setup

### **Test Runners**
- **Unit/Component/Integration**: Vitest + Vue Test Utils + JSDOM
- **E2E**: Playwright (Chromium, Firefox, WebKit, Mobile)
- **Node Version**: v20 in CI

### **Vue Testing Stack**
- **Component Testing**: Vue Test Utils
- **State Management**: Pinia + `createTestingPinia` for tests
- **DOM Environment**: JSDOM for unit/component tests

## 📁 Directory Structure

```
tests/
├── unit/                    # Pure logic and simple component methods
├── components/              # Isolated component behaviour
├── integration/             # Component + store + mocked API (*.integration.spec.js)
├── e2e/
│   ├── critical/           # P0 critical flows tagged @critical
│   ├── user-journeys/      # Multi-page flows
│   └── regression/         # Curated regression set tagged @regression
└── config/
    ├── playwright.config.js
    └── vitest.config.js
```

## 🏷️ Tagging Conventions (Playwright)

### **Critical Tests** (`@critical`)
- **Must run on every PR** (fast subset)
- **Examples**: Login, password reset, payment flows
- **Run**: `npx playwright test --grep @critical`

### **Regression Tests** (`@regression`)
- **Runs nightly and on demand**
- **Examples**: Profile updates, settings persistence
- **Run**: `npx playwright test --grep @regression`

## 🎯 Test Types & When to Use

### **Unit Tests**
- **What**: Pure logic, small functions, component methods
- **Examples**: Form validation, utility functions, computed properties
- **Location**: `tests/unit/`
- **Command**: `npm run test:unit`

### **Component Tests**
- **What**: Single Vue component mounted in isolation
- **Examples**: Button clicks, prop validation, event emission
- **Location**: `tests/components/`
- **Command**: `npm run test:unit`

### **Integration Tests**
- **What**: Component + store + mocked API (no real navigation)
- **Examples**: Login flow with store updates, form submission with API calls
- **Location**: `tests/integration/` (use `*.integration.spec.js`)
- **Command**: `npm run test:unit`

### **E2E Tests**
- **What**: Full browser flow with real navigation
- **Examples**: Complete user journeys, cross-page workflows
- **Location**: `tests/e2e/`
- **Command**: `npm run test:e2e`

## 🎨 Best Practices

### **Selectors**
- **Prefer `data-cy`** over text for brittle elements
- **Example**: `<button data-cy="login-submit">Login</button>`
- **Why**: More stable than text or CSS selectors

### **Naming Conventions**
- **Use kebab-case** with clear intent
- **Examples**: `login.spec.js`, `user-profile.integration.spec.js`
- **One main "happy path"** per file; add edge cases nearby

### **Mocking Strategy**
- **API Calls**: Use `page.route()` in E2E, `vi.mock()` in unit tests
- **Store**: Use `createTestingPinia()` for Pinia stores
- **Router**: Mock router methods in component tests

## 🚀 Quick Commands

```bash
# Unit & Component Tests
npm run test:unit

# E2E Tests (local Chromium)
npm run test:e2e

# E2E Critical Only
npx playwright test --grep @critical

# E2E Regression Only
npx playwright test --grep @regression

# Run with UI
npm run test:unit -- --ui
npx playwright test --ui
```

## 🔄 CI Integration

### **When Tests Run**
- **Local Development**: Fast checks; Chromium only for E2E
- **PR/Push to staging**: Lint + Unit/Component + E2E @critical (Chromium)
- **Nightly (schedule)**: E2E @regression across all browsers
- **Manual**: Choose suite via workflow_dispatch

### **CI Policy**
- **PRs**: Fast feedback (Chromium-only, @critical)
- **Nightly**: Full cross-browser regression
- **Retries**: Enabled in CI to reduce flakes
- **Concurrency**: Cancels duplicate runs

## 📝 How to Add a New Test

### **E2E Test**
1. **Place critical flows** under `tests/e2e/critical/` and tag with `@critical`
2. **Place regression-only flows** under `tests/e2e/regression/` and tag with `@regression`
3. **Use `page.route()`** to mock network calls where needed

### **Unit/Component Test**
1. **Place in appropriate directory** (`unit/`, `components/`, `integration/`)
2. **Use descriptive test names** that explain the scenario
3. **Mock external dependencies** (API, router, store)

### **Example E2E Test**
```javascript
import { test, expect } from '@playwright/test';

test('user can login successfully @critical', async ({ page }) => {
  await page.goto('/login');
  await page.fill('[data-cy="email"]', '<EMAIL>');
  await page.fill('[data-cy="password"]', 'password123');
  await page.click('[data-cy="login-submit"]');
  
  await expect(page).toHaveURL('/dashboard');
  await expect(page.locator('[data-cy="user-menu"]')).toBeVisible();
});
```

## 🔧 Troubleshooting

### **Common Issues**
- **Flaky tests**: Use stable selectors, add waits for network calls
- **Slow tests**: Mock heavy operations, use `vi.mock()` for external APIs
- **CI failures**: Check browser compatibility, add retries for network-dependent tests

### **Debugging**
- **E2E**: Use `--headed` flag to see browser, add `page.pause()` for debugging
- **Unit**: Use `--ui` flag for interactive debugging
- **CI**: Check Playwright HTML report for detailed failure information

## 📚 Resources

- [Vue Test Utils Documentation](https://test-utils.vuejs.org/)
- [Playwright Documentation](https://playwright.dev/)
- [Vitest Documentation](https://vitest.dev/)

---

**Last Updated**: 2025-01-20  
**Maintainer**: Frontend QA Team
