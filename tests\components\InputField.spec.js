import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import InputField from '@/ui-kit/InputField.vue'

describe('InputField Component', () => {
  it('should render with basic props', () => {
    const wrapper = mount(InputField, {
      props: {
        id: 'test-input',
        modelValue: '',
        placeholder: 'Enter text'
      }
    })

    expect(wrapper.find('input').exists()).toBe(true)
    expect(wrapper.find('input').attributes('placeholder')).toBe('Enter text')
  })

  it('should emit update:modelValue when input changes', async () => {
    const wrapper = mount(InputField, {
      props: {
        id: 'test-input',
        modelValue: ''
      }
    })

    const input = wrapper.find('input')
    await input.setValue('new value')
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')[0]).toEqual(['new value'])
  })

  it('should handle password type', () => {
    const wrapper = mount(InputField, {
      props: {
        id: 'password-input',
        modelValue: '',
        type: 'password'
      }
    })

    expect(wrapper.find('input').attributes('type')).toBe('password')
  })

  it('should show required tag when requireTag is true', () => {
    const wrapper = mount(InputField, {
      props: {
        id: 'test-input',
        modelValue: '',
        requireTag: true
      }
    })

    expect(wrapper.find('input').attributes('required')).toBeDefined()
  })

  it('should be disabled when disabled prop is true', () => {
    const wrapper = mount(InputField, {
      props: {
        id: 'test-input',
        modelValue: '',
        disabled: true
      }
    })

    expect(wrapper.find('input').attributes('disabled')).toBeDefined()
  })

  it('should render with icon when provided', () => {
    const wrapper = mount(InputField, {
      props: {
        id: 'test-input',
        modelValue: '',
        icon: 'UserSvg'
      }
    })

    // Check if icon span exists (the span that contains the icon)
    const iconSpan = wrapper.find('span')
    expect(iconSpan.exists()).toBe(true)
    expect(iconSpan.classes()).toContain('absolute')
  })
})
