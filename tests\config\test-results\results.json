{"config": {"configFile": "C:\\Users\\<USER>\\Documents\\github\\frontend\\tests\\config\\playwright.config.js", "rootDir": "C:/Users/<USER>/Documents/github/frontend/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 5}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Documents/github/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Documents/github/frontend/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "user-journeys\\company-onboarding.spec.js", "file": "user-journeys/company-onboarding.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "@user-journey @critical User Journey - Company Onboarding", "file": "user-journeys/company-onboarding.spec.js", "line": 6, "column": 6, "specs": [{"title": "should navigate from pre-registration to company registration @regression", "ok": false, "tags": ["user-journey", "critical", "regression"], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 31035, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Documents\\github\\frontend\\tests\\e2e\\user-journeys\\company-onboarding.spec.js", "column": 26, "line": 69}, "message": "Error: locator.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for getBy<PERSON>ole('button', { name: 'Create Account' })\u001b[22m\n\u001b[2m    - locator resolved to <button disabled data-cy=\"register-button\" class=\"flex items-center h-[2.625rem] justify-center gap-1 px-4 py-3 font-medium rounded-xl focus:outline-none focus:ring transition ease-in-out cursor-pointer text-white bg-[#BBBCBE] hover:bg-gray3 hover:cursor-not-allowed w-full\">…</button>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not enabled\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m    51 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not enabled\u001b[22m\n\u001b[2m     - retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\n\n\u001b[0m \u001b[90m 67 |\u001b[39m\n \u001b[90m 68 |\u001b[39m     \u001b[36mconst\u001b[39m registerButton \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'button'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Create Account'\u001b[39m })\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m registerButton\u001b[33m.\u001b[39mclick()\n \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m\n \u001b[90m 71 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[32m'/registration/company-registration'\u001b[39m)\n \u001b[90m 72 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'heading'\u001b[39m\u001b[33m,\u001b[39m { name\u001b[33m:\u001b[39m \u001b[32m'Join as a Tech Company'\u001b[39m }))\u001b[33m.\u001b[39mtoBeVisible()\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\github\\frontend\\tests\\e2e\\user-journeys\\company-onboarding.spec.js:69:26\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-18T12:41:33.550Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\github\\frontend\\test-results\\user-journeys-company-onbo-3e353-any-registration-regression-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\github\\frontend\\test-results\\user-journeys-company-onbo-3e353-any-registration-regression-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\github\\frontend\\test-results\\user-journeys-company-onbo-3e353-any-registration-regression-chromium\\error-context.md"}]}], "status": "unexpected"}], "id": "a74ea89b56e6f5b6d25b-f9d05a97f94e22f88cd0", "file": "user-journeys/company-onboarding.spec.js", "line": 56, "column": 3}, {"title": "should display company registration form correctly", "ok": true, "tags": ["user-journey", "critical"], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 3310, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-18T12:41:33.563Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a74ea89b56e6f5b6d25b-4d0fb868d6bb7c3cf86b", "file": "user-journeys/company-onboarding.spec.js", "line": 75, "column": 3}, {"title": "should handle form validation - button disabled initially", "ok": true, "tags": ["user-journey", "critical"], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 3297, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-18T12:41:33.553Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a74ea89b56e6f5b6d25b-e484b76e863201da5576", "file": "user-journeys/company-onboarding.spec.js", "line": 101, "column": 3}, {"title": "should handle API errors gracefully", "ok": true, "tags": ["user-journey", "critical"], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 3308, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-18T12:41:33.552Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "a74ea89b56e6f5b6d25b-666fbd606d98a79097df", "file": "user-journeys/company-onboarding.spec.js", "line": 108, "column": 3}, {"title": "should fill form fields and verify they accept input", "ok": false, "tags": ["user-journey", "critical"], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 8656, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeChecked\u001b[2m()\u001b[22m\n\nLocator: locator('input[id=\"terms-checkbox\"]')\nExpected: checked\nReceived: unchecked\nCall log:\n\u001b[2m  - Expect \"toBeChecked\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[id=\"terms-checkbox\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to <input data-cy=\"\" class=\"hidden\" type=\"checkbox\" id=\"terms-checkbox\"/>\u001b[22m\n\u001b[2m      - unexpected value \"unchecked\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeChecked\u001b[2m()\u001b[22m\n\nLocator: locator('input[id=\"terms-checkbox\"]')\nExpected: checked\nReceived: unchecked\nCall log:\n\u001b[2m  - Expect \"toBeChecked\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[id=\"terms-checkbox\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to <input data-cy=\"\" class=\"hidden\" type=\"checkbox\" id=\"terms-checkbox\"/>\u001b[22m\n\u001b[2m      - unexpected value \"unchecked\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Documents\\github\\frontend\\tests\\e2e\\user-journeys\\company-onboarding.spec.js:159:62", "location": {"file": "C:\\Users\\<USER>\\Documents\\github\\frontend\\tests\\e2e\\user-journeys\\company-onboarding.spec.js", "column": 62, "line": 159}, "snippet": "\u001b[0m \u001b[90m 157 |\u001b[39m     \u001b[90m// Verify checkboxes are checked\u001b[39m\n \u001b[90m 158 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[id=\"company-checkbox\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeChecked()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 159 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[id=\"terms-checkbox\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeChecked()\n \u001b[90m     |\u001b[39m                                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 160 |\u001b[39m   })\n \u001b[90m 161 |\u001b[39m })\n \u001b[90m 162 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\github\\frontend\\tests\\e2e\\user-journeys\\company-onboarding.spec.js", "column": 62, "line": 159}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeChecked\u001b[2m()\u001b[22m\n\nLocator: locator('input[id=\"terms-checkbox\"]')\nExpected: checked\nReceived: unchecked\nCall log:\n\u001b[2m  - Expect \"toBeChecked\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[id=\"terms-checkbox\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to <input data-cy=\"\" class=\"hidden\" type=\"checkbox\" id=\"terms-checkbox\"/>\u001b[22m\n\u001b[2m      - unexpected value \"unchecked\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 157 |\u001b[39m     \u001b[90m// Verify checkboxes are checked\u001b[39m\n \u001b[90m 158 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[id=\"company-checkbox\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeChecked()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 159 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[id=\"terms-checkbox\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeChecked()\n \u001b[90m     |\u001b[39m                                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 160 |\u001b[39m   })\n \u001b[90m 161 |\u001b[39m })\n \u001b[90m 162 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\github\\frontend\\tests\\e2e\\user-journeys\\company-onboarding.spec.js:159:62\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-18T12:41:33.552Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\github\\frontend\\test-results\\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\\test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\github\\frontend\\test-results\\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\\test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\github\\frontend\\test-results\\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\github\\frontend\\test-results\\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\github\\frontend\\test-results\\user-journeys-company-onbo-5dbf1-nd-verify-they-accept-input-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\github\\frontend\\tests\\e2e\\user-journeys\\company-onboarding.spec.js", "column": 62, "line": 159}}], "status": "unexpected"}], "id": "a74ea89b56e6f5b6d25b-1a042fb8e3d1d9dcb632", "file": "user-journeys/company-onboarding.spec.js", "line": 132, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-18T12:41:30.758Z", "duration": 34261.089, "expected": 3, "skipped": 0, "unexpected": 2, "flaky": 0}}