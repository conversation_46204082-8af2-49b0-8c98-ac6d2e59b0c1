// 🧪 User Journey Test: Complete Company Onboarding Flow
// Tests the actual SkilledUp Life company registration and onboarding process

import { test, expect } from '@playwright/test'

test.describe('@user-journey @critical User Journey - Company Onboarding', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses for the entire flow with correct payload shapes
    await page.route('**/countries', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            { id: 1, name: 'United Kingdom', code: 'GB', phone: '+44' },
            { id: 2, name: 'United States', code: 'US', phone: '+1' }
          ]
        })
      })
    })

    await page.route('**/company-categories', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            { id: 1, name: 'Software Development' },
            { id: 2, name: 'Fintech' }
          ]
        })
      })
    })

    await page.route('**/register', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            token: 'mock-company-token',
            user: {
              id: 1,
              role: ['company'],
              email_verified: false,
              profile_step: 1,
              name: 'testcompany'
            }
          }
        })
      })
    })
  })

  test('should navigate from pre-registration to company registration @regression', async ({ page }) => {
    await page.goto('/registration/pre-registration')

    await expect(page.getByRole('heading', { name: 'Join as a Volunteer or Tech Company' })).toBeVisible()
    await expect(page.getByText("I'm a Tech Company")).toBeVisible()
    await expect(page.getByText('I am a Volunteer')).toBeVisible()

    const companyCard = page.locator('div').filter({ hasText: "I'm a Tech Company" }).first()
    await companyCard.click()

    await page.waitForTimeout(300)

    const registerButton = page.getByRole('button', { name: 'Create Account' })
    await registerButton.click()

    await expect(page).toHaveURL('/registration/company-registration')
    await expect(page.getByRole('heading', { name: 'Join as a Tech Company' })).toBeVisible()
  })

  test('should display company registration form correctly', async ({ page }) => {
    await page.goto('/registration/company-registration')

    await expect(page.getByRole('heading', { name: 'Join as a Tech Company' })).toBeVisible()

    // Verify form fields are present
    await expect(page.locator('input[id="name"]')).toBeVisible()
    await expect(page.locator('input[id="firstName"]')).toBeVisible()
    await expect(page.locator('input[id="lastName"]')).toBeVisible()
    await expect(page.locator('input[id="companyName"]')).toBeVisible()
    await expect(page.locator('input[id="email"]')).toBeVisible()
    await expect(page.locator('input[id="password"]')).toBeVisible()
    await expect(page.locator('input[id="confirmPassword"]')).toBeVisible()

    // Verify dropdowns are present
    await expect(page.locator('input[placeholder="Select your country"]')).toBeVisible()
    await expect(page.locator('input[placeholder="Select category"]')).toBeVisible()

    // Verify checkboxes are present
    await expect(page.locator('label[for="company-checkbox"]')).toBeVisible()
    await expect(page.locator('label[for="terms-checkbox"]')).toBeVisible()

    // Verify submit button is present
    await expect(page.getByRole('button', { name: 'Create an account' })).toBeVisible()
  })

  test('should handle form validation - button disabled initially', async ({ page }) => {
    await page.goto('/registration/company-registration')

    const submitButton = page.getByRole('button', { name: 'Create an account' })
    await expect(submitButton).toBeDisabled()
  })

  test('should handle API errors gracefully', async ({ page }) => {
    await page.route('**/register', route => {
      route.fulfill({
        status: 422,
        contentType: 'application/json',
        body: JSON.stringify({
          errors: {
            email: ['This email is already taken'],
            username: ['Username must be unique']
          }
        })
      })
    })

    await page.goto('/registration/company-registration')

    await page.fill('input[id="name"]', 'existinguser')
    await page.fill('input[id="email"]', '<EMAIL>')
    await page.fill('input[id="password"]', 'Password123!')
    await page.fill('input[id="confirmPassword"]', 'Password123!')

    await expect(page.getByText('This email is already taken')).not.toBeVisible()
  })

  test('should fill form fields and verify they accept input', async ({ page }) => {
    await page.goto('/registration/company-registration')

    // Fill form fields
    await page.fill('input[id="name"]', 'testcompany')
    await page.fill('input[id="firstName"]', 'Jane')
    await page.fill('input[id="lastName"]', 'Smith')
    await page.fill('input[id="companyName"]', 'Tech Solutions Inc')
    await page.fill('input[id="email"]', '<EMAIL>')
    await page.fill('input[id="password"]', 'Password123!')
    await page.fill('input[id="confirmPassword"]', 'Password123!')

    // Verify fields contain the input
    await expect(page.locator('input[id="name"]')).toHaveValue('testcompany')
    await expect(page.locator('input[id="firstName"]')).toHaveValue('Jane')
    await expect(page.locator('input[id="lastName"]')).toHaveValue('Smith')
    await expect(page.locator('input[id="companyName"]')).toHaveValue('Tech Solutions Inc')
    await expect(page.locator('input[id="email"]')).toHaveValue('<EMAIL>')
    await expect(page.locator('input[id="password"]')).toHaveValue('Password123!')
    await expect(page.locator('input[id="confirmPassword"]')).toHaveValue('Password123!')

    // Test checkbox interactions
    await page.click('label[for="company-checkbox"]')
    await page.click('label[for="terms-checkbox"]')

    // Verify checkboxes are checked
    await expect(page.locator('input[id="company-checkbox"]')).toBeChecked()
    await expect(page.locator('input[id="terms-checkbox"]')).toBeChecked()
  })
})
