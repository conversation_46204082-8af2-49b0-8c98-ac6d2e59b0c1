import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import CheckBox from '@/ui-kit/CheckBox.vue'

describe('CheckBox Component', () => {
  it('should render with label', () => {
    const wrapper = mount(CheckBox, {
      props: {
        label: 'Remember me',
        fieldId: 'remember-me',
        modelValue: false
      }
    })

    expect(wrapper.text()).toContain('Remember me')
  })

  it('should emit update:modelValue when checked', async () => {
    const wrapper = mount(CheckBox, {
      props: {
        label: 'Remember me',
        fieldId: 'remember-me',
        modelValue: false
      }
    })

    await wrapper.setProps({ modelValue: true })
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
  })

  it('should emit update:modelValue when unchecked', async () => {
    const wrapper = mount(CheckBox, {
      props: {
        label: 'Remember me',
        fieldId: 'remember-me',
        modelValue: true
      }
    })

    await wrapper.setProps({ modelValue: false })
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
  })

  it('should be checked when modelValue is true', () => {
    const wrapper = mount(CheckBox, {
      props: {
        label: 'Remember me',
        fieldId: 'remember-me',
        modelValue: true
      }
    })

    expect(wrapper.find('input[type="checkbox"]').element.checked).toBe(true)
  })

  it('should be disabled when disabled prop is true', () => {
    const wrapper = mount(CheckBox, {
      props: {
        label: 'Remember me',
        fieldId: 'remember-me',
        modelValue: false,
        disabled: true
      }
    })

    expect(wrapper.find('input[type="checkbox"]').attributes('disabled')).toBeDefined()
  })

  it('should have correct fieldId attribute', () => {
    const wrapper = mount(CheckBox, {
      props: {
        label: 'Remember me',
        fieldId: 'remember-me',
        modelValue: false
      }
    })

    expect(wrapper.find('input[type="checkbox"]').attributes('id')).toBe('remember-me')
  })

  it('should handle click events', async () => {
    const wrapper = mount(CheckBox, {
      props: {
        label: 'Remember me',
        fieldId: 'remember-me',
        modelValue: false
      }
    })

    // Set the input value and trigger change
    const input = wrapper.find('input[type="checkbox"]')
    await input.setValue(true)
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
  })
})
