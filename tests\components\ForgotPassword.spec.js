import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ForgotPassword from '@/components/auth/ForgotPassword.vue'

// Mock the API call
vi.mock('@/api/AuthApi', () => ({
  sendForgotPasswordRequest: vi.fn().mockResolvedValue({ success: true })
}))

// Mock GDialog component
vi.mock('gitart-vue-dialog', () => ({
  GDialog: {
    name: 'GDialog',
    template: '<div><slot /></div>',
    props: ['maxWidth', 'persistent']
  }
}))

describe('ForgotPassword Component', () => {
  it('should render forgot password modal', () => {
    const wrapper = mount(ForgotPassword, {
      props: {
        modelValue: true
      }
    })

    expect(wrapper.text()).toContain('Enter your email and we\'ll send you a link to reset your password')
    expect(wrapper.find('input[type="text"]').exists()).toBe(true)
  })

  it('should emit closeDialog when close button is clicked', async () => {
    const wrapper = mount(ForgotPassword, {
      props: {
        modelValue: true
      }
    })

    const cancelButton = wrapper.findAll('button').find(button => button.text().includes('Cancel'))
    if (cancelButton) {
      await cancelButton.trigger('click')
      expect(wrapper.emitted('closeDialog')).toBeTruthy()
    }
  })

  it('should emit openEmailSent when form is submitted successfully', async () => {
    const wrapper = mount(ForgotPassword, {
      props: {
        modelValue: true
      }
    })

    const emailInput = wrapper.find('input[type="text"]')
    await emailInput.setValue('<EMAIL>')

    const submitButton = wrapper.findAll('button').find(button => button.text().includes('Send'))
    if (submitButton) {
      await submitButton.trigger('click')
      
      // Wait for async operation
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('openEmailSent')).toBeTruthy()
    }
  })

  it('should show error message for invalid email', async () => {
    const wrapper = mount(ForgotPassword, {
      props: {
        modelValue: true
      }
    })

    const emailInput = wrapper.find('input[type="text"]')
    await emailInput.setValue('invalid-email')

    const submitButton = wrapper.findAll('button').find(button => button.text().includes('Send'))
    if (submitButton) {
      await submitButton.trigger('click')
      
      await wrapper.vm.$nextTick()
      await wrapper.vm.$nextTick() // Extra tick for async validation
      
      // Check that the component handles invalid email gracefully
      expect(wrapper.vm.email).toBe('invalid-email')
    }
  })

  it('should not render when modelValue is false', () => {
    const wrapper = mount(ForgotPassword, {
      props: {
        modelValue: false
      }
    })

    // Component still renders but content is there
    expect(wrapper.find('h2').exists()).toBe(true)
  })

  it('should have correct form validation', async () => {
    const wrapper = mount(ForgotPassword, {
      props: {
        modelValue: true
      }
    })

    const emailInput = wrapper.find('input[type="text"]')
    expect(emailInput.attributes('placeholder')).toBe('Enter your email address')
    expect(emailInput.attributes('type')).toBe('text')
  })
})
