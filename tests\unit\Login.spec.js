// 🧪 Unit Tests: Login Component
// Tests the Login.vue component functionality

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import Login from '@/pages/auth/Login.vue'

// Mock stores
const mockAuthStore = {
  login: vi.fn(),
  email_verified: true,
  role: 'volunteer',
  profileStep: 5
}

const mockVolunteerStore = {
  // Add volunteer store methods if needed
}

vi.mock('@/store/auth', () => ({
  useAuthStore: () => mockAuthStore
}))

vi.mock('@/store/volunteerStore', () => ({
  useVolunteerStore: () => mockVolunteerStore
}))

// Mock router
const mockRouter = {
  push: vi.fn()
}

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')
  return {
    ...actual,
    useRouter: () => mockRouter
  }
})

// Mock localStorage and sessionStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}

const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

describe('Login Component', () => {
  let wrapper
  let pinia
  let router

  beforeEach(() => {
    // Create fresh instances for each test
    pinia = createPinia()
    setActivePinia(pinia)
    
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/volunteer/dashboard', name: 'volunteer-dashboard' },
        { path: '/company/dashboard', name: 'company-dashboard' },
        { path: '/system/dashboard', name: 'system-dashboard' },
        { path: '/verify-email', name: 'verify-email' },
        { path: '/volunteer-profile-creation', name: 'volunteer-profile-creation' },
        { path: '/company-profile-creation', name: 'company-profile-creation' }
      ]
    })

    // Reset mocks
    vi.clearAllMocks()
    mockAuthStore.login.mockResolvedValue()
    mockRouter.push.mockResolvedValue()
    localStorageMock.getItem.mockReturnValue(null)
    sessionStorageMock.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  const createWrapper = () => {
    return mount(Login, {
      global: {
        plugins: [pinia, router],
        stubs: {
          'InputField': true,
          'Button': true,
          'CheckBox': true,
          'Link': true,
          'ForgotPassword': true,
          'EmailSent': true,
          'PasswordSvg': true,
          'UserSvg': true
        }
      }
    })
  }

  describe('Component Rendering', () => {
    it('should render login form with all required elements', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('h2').text()).toBe('Welcome to SkilledUp Life')
      expect(wrapper.find('p').text()).toBe('Sign in to continue')
      expect(wrapper.findComponent({ name: 'InputField' })).toBeTruthy()
      expect(wrapper.findComponent({ name: 'Button' })).toBeTruthy()
      expect(wrapper.findComponent({ name: 'CheckBox' })).toBeTruthy()
      expect(wrapper.find('a[href="#"]').text()).toBe('Forgot password?')
    })

    it('should render registration link', () => {
      wrapper = createWrapper()
      
      const registerLink = wrapper.findComponent({ name: 'Link' })
      expect(registerLink.props('href')).toBe('/registration/pre-registration')
      expect(registerLink.props('linkText')).toBe("Don't have an account?")
    })
  })

  describe('Form Validation', () => {
    it('should disable login button when form is invalid', () => {
      wrapper = createWrapper()
      
      const loginButton = wrapper.findComponent({ name: 'Button' })
      expect(loginButton.props('disabled')).toBe(true)
    })
  })

  describe('Login Functionality', () => {
    it('should call auth store login when form is submitted', async () => {
      wrapper = createWrapper()
      
      // Simulate user input by triggering events on the InputField components
      const inputFields = wrapper.findAllComponents({ name: 'InputField' })
      
      // Set email
      await inputFields[0].vm.$emit('update:modelValue', '<EMAIL>')
      
      // Set password
      await inputFields[1].vm.$emit('update:modelValue', 'password123')
      
      // Click login button
      const loginButton = wrapper.findComponent({ name: 'Button' })
      await loginButton.trigger('click')
      
      expect(mockAuthStore.login).toHaveBeenCalledWith(
        '<EMAIL>',
        'password123',
        false // rememberMe default value
      )
    })

    it('should redirect to volunteer dashboard on successful login', async () => {
      wrapper = createWrapper()
      mockAuthStore.role = 'volunteer'
      mockAuthStore.email_verified = true
      mockAuthStore.profileStep = 7 // Complete profile (max step for volunteer)
      
      // Simulate user input and login
      const inputFields = wrapper.findAllComponents({ name: 'InputField' })
      await inputFields[0].vm.$emit('update:modelValue', '<EMAIL>')
      await inputFields[1].vm.$emit('update:modelValue', 'password123')
      
      const loginButton = wrapper.findComponent({ name: 'Button' })
      await loginButton.trigger('click')
      
      expect(mockRouter.push).toHaveBeenCalledWith('/volunteer/dashboard')
    })

    it('should redirect to company dashboard for company users', async () => {
      wrapper = createWrapper()
      mockAuthStore.role = 'company'
      mockAuthStore.email_verified = true
      mockAuthStore.profileStep = 5
      
      // Simulate user input and login
      const inputFields = wrapper.findAllComponents({ name: 'InputField' })
      await inputFields[0].vm.$emit('update:modelValue', '<EMAIL>')
      await inputFields[1].vm.$emit('update:modelValue', 'password123')
      
      const loginButton = wrapper.findComponent({ name: 'Button' })
      await loginButton.trigger('click')
      
      expect(mockRouter.push).toHaveBeenCalledWith('/company/dashboard')
    })

    it('should redirect to system dashboard for superadmin', async () => {
      wrapper = createWrapper()
      mockAuthStore.role = 'superadmin'
      mockAuthStore.email_verified = true
      
      // Simulate user input and login
      const inputFields = wrapper.findAllComponents({ name: 'InputField' })
      await inputFields[0].vm.$emit('update:modelValue', '<EMAIL>')
      await inputFields[1].vm.$emit('update:modelValue', 'password123')
      
      const loginButton = wrapper.findComponent({ name: 'Button' })
      await loginButton.trigger('click')
      
      expect(mockRouter.push).toHaveBeenCalledWith('/system/dashboard')
    })

    it('should redirect to email verification if email not verified', async () => {
      wrapper = createWrapper()
      mockAuthStore.email_verified = false
      
      // Simulate user input and login
      const inputFields = wrapper.findAllComponents({ name: 'InputField' })
      await inputFields[0].vm.$emit('update:modelValue', '<EMAIL>')
      await inputFields[1].vm.$emit('update:modelValue', 'password123')
      
      const loginButton = wrapper.findComponent({ name: 'Button' })
      await loginButton.trigger('click')
      
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'verify-email' })
    })

    it('should redirect to profile creation if profile incomplete', async () => {
      wrapper = createWrapper()
      mockAuthStore.role = 'volunteer'
      mockAuthStore.email_verified = true
      mockAuthStore.profileStep = 1 // Incomplete profile
      
      // Simulate user input and login
      const inputFields = wrapper.findAllComponents({ name: 'InputField' })
      await inputFields[0].vm.$emit('update:modelValue', '<EMAIL>')
      await inputFields[1].vm.$emit('update:modelValue', 'password123')
      
      const loginButton = wrapper.findComponent({ name: 'Button' })
      await loginButton.trigger('click')
      
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'volunteer-profile-creation' })
    })
  })

  describe('Error Handling', () => {
    it('should display error message when login fails', async () => {
      wrapper = createWrapper()
      const errorMessage = 'Invalid credentials'
      mockAuthStore.login.mockRejectedValue(errorMessage)
      
      // Simulate user input and login
      const inputFields = wrapper.findAllComponents({ name: 'InputField' })
      await inputFields[0].vm.$emit('update:modelValue', '<EMAIL>')
      await inputFields[1].vm.$emit('update:modelValue', 'password123')
      
      const loginButton = wrapper.findComponent({ name: 'Button' })
      await loginButton.trigger('click')
      
      // Wait for error to be displayed
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain(errorMessage)
    })
  })

  describe('Remember Me Functionality', () => {
    it('should load saved email from localStorage on mount', async () => {
      const savedEmail = '<EMAIL>'
      localStorageMock.getItem.mockReturnValue(savedEmail)
      
      wrapper = createWrapper()
      
      // Wait for component to mount and process localStorage
      await wrapper.vm.$nextTick()
      
      // Check that the email input field has the saved value
      const emailInput = wrapper.findComponent({ name: 'InputField' })
      expect(emailInput.props('modelValue')).toBe(savedEmail)
    })

    it('should not load email if none saved in localStorage', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      wrapper = createWrapper()
      
      // Check that the email input field is empty
      const emailInput = wrapper.findComponent({ name: 'InputField' })
      expect(emailInput.props('modelValue')).toBe('')
    })
  })

  describe('Forgot Password Modal', () => {
    it('should open forgot password modal when link is clicked', async () => {
      wrapper = createWrapper()
      
      const forgotPasswordLink = wrapper.find('a[href="#"]')
      await forgotPasswordLink.trigger('click')
      
      // Wait for the modal to open
      await wrapper.vm.$nextTick()
      
      // Check that the forgot password modal is visible
      const forgotPasswordModal = wrapper.findComponent({ name: 'ForgotPassword' })
      expect(forgotPasswordModal.exists()).toBe(true)
    })
  })

  describe('Session Storage Handling', () => {
    it('should redirect to saved volunteer profile creation URL', async () => {
      wrapper = createWrapper()
      const savedUrl = '/volunteer/profile/step2'
      sessionStorageMock.getItem.mockReturnValue(savedUrl)
      mockAuthStore.role = 'volunteer'
      mockAuthStore.email_verified = true
      mockAuthStore.profileStep = 7 // Complete profile (max step for volunteer)
      
      // Simulate user input and login
      const inputFields = wrapper.findAllComponents({ name: 'InputField' })
      await inputFields[0].vm.$emit('update:modelValue', '<EMAIL>')
      await inputFields[1].vm.$emit('update:modelValue', 'password123')
      
      const loginButton = wrapper.findComponent({ name: 'Button' })
      await loginButton.trigger('click')
      
      expect(mockRouter.push).toHaveBeenCalledWith(savedUrl)
      expect(sessionStorageMock.removeItem).toHaveBeenCalledWith('volunteerProfileCreation')
    })
  })
})
