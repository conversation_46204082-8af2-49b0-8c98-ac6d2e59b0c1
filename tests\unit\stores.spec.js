// 🧪 Unit Tests: Pinia Stores
// Tests the Pinia stores used in registration and profile creation

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useVolunteerStore } from '@/store/volunteerStore'
import { useCompanyStore } from '@/store/CompanyStore'
import { useProfileCreationStore } from '@/store/profileCreationStore'
import { useCompanyProfileCreationStore } from '@/store/CompanyProfileCreation'

describe('Pinia Stores', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    vi.clearAllMocks()
  })

  describe('Volunteer Store', () => {
    it('should initialize with default values', () => {
      const store = useVolunteerStore()
      
      expect(store.username).toBe('')
      expect(store.firstName).toBe('')
      expect(store.lastName).toBe('')
      expect(store.email).toBe('')
      expect(store.password).toBe('')
      expect(store.confirmPassword).toBe('')
      expect(store.selectedCountry).toBe(null)
      expect(store.selectedCategory).toBe(null)
      expect(store.isTerms).toBe(false)
      expect(store.isVolunteer).toBe(false)
    })

    it('should update fields correctly', () => {
      const store = useVolunteerStore()
      
      store.username = 'testvolunteer'
      store.firstName = 'John'
      store.lastName = 'Doe'
      store.email = '<EMAIL>'
      
      expect(store.username).toBe('testvolunteer')
      expect(store.firstName).toBe('John')
      expect(store.lastName).toBe('Doe')
      expect(store.email).toBe('<EMAIL>')
    })

    it('should reset form correctly', () => {
      const store = useVolunteerStore()
      
      // Set some values
      store.username = 'testvolunteer'
      store.firstName = 'John'
      store.email = '<EMAIL>'
      
      // Reset form
      store.resetForm()
      
      expect(store.username).toBe('')
      expect(store.firstName).toBe('')
      expect(store.email).toBe('')
      expect(store.password).toBe('')
      expect(store.confirmPassword).toBe('')
      expect(store.selectedCountry).toBe(null)
      expect(store.selectedCategory).toBe(null)
      expect(store.isTerms).toBe(false)
      expect(store.isVolunteer).toBe(false)
    })

    it('should validate form correctly', () => {
      const store = useVolunteerStore()
      
      // Initially form should be invalid
      expect(store.isFormValid).toBe(false)
      
      // Fill required fields
      store.username = 'testvolunteer'
      store.firstName = 'John'
      store.lastName = 'Doe'
      store.email = '<EMAIL>'
      store.password = 'Password123!'
      store.confirmPassword = 'Password123!'
      store.selectedCountry = 1
      store.selectedCategory = 1
      store.isTerms = true
      store.isVolunteer = true
      
      // Form should now be valid
      expect(store.isFormValid).toBe(true)
    })

    it('should validate password match', () => {
      const store = useVolunteerStore()
      
      store.password = 'Password123!'
      store.confirmPassword = 'Password123!'
      
      expect(store.passwordsMatch).toBe(true)
      
      store.confirmPassword = 'DifferentPassword123!'
      
      expect(store.passwordsMatch).toBe(false)
    })

    it('should validate email format', () => {
      const store = useVolunteerStore()
      
      store.email = '<EMAIL>'
      expect(store.isValidEmail).toBe(true)
      
      store.email = 'invalid-email'
      expect(store.isValidEmail).toBe(false)
      
      store.email = ''
      expect(store.isValidEmail).toBe(false)
    })
  })

  describe('Company Store', () => {
    it('should initialize with default values', () => {
      const store = useCompanyStore()
      
      expect(store.username).toBe('')
      expect(store.firstName).toBe('')
      expect(store.lastName).toBe('')
      expect(store.companyName).toBe('')
      expect(store.email).toBe('')
      expect(store.password).toBe('')
      expect(store.confirmPassword).toBe('')
      expect(store.selectedCountry).toBe(null)
      expect(store.selectedCategory).toBe(null)
      expect(store.isTerms).toBe(false)
      expect(store.isCompany).toBe(false)
    })

    it('should update fields correctly', () => {
      const store = useCompanyStore()
      
      store.username = 'testcompany'
      store.firstName = 'Jane'
      store.lastName = 'Smith'
      store.companyName = 'Tech Solutions Inc'
      store.email = '<EMAIL>'
      
      expect(store.username).toBe('testcompany')
      expect(store.firstName).toBe('Jane')
      expect(store.lastName).toBe('Smith')
      expect(store.companyName).toBe('Tech Solutions Inc')
      expect(store.email).toBe('<EMAIL>')
    })

    it('should reset form correctly', () => {
      const store = useCompanyStore()
      
      // Set some values
      store.username = 'testcompany'
      store.companyName = 'Tech Solutions Inc'
      store.email = '<EMAIL>'
      
      // Reset form
      store.resetForm()
      
      expect(store.username).toBe('')
      expect(store.firstName).toBe('')
      expect(store.lastName).toBe('')
      expect(store.companyName).toBe('')
      expect(store.email).toBe('')
      expect(store.password).toBe('')
      expect(store.confirmPassword).toBe('')
      expect(store.selectedCountry).toBe(null)
      expect(store.selectedCategory).toBe(null)
      expect(store.isTerms).toBe(false)
      expect(store.isCompany).toBe(false)
    })

    it('should validate form correctly', () => {
      const store = useCompanyStore()
      
      // Initially form should be invalid
      expect(store.isFormValid).toBe(false)
      
      // Fill required fields
      store.username = 'testcompany'
      store.firstName = 'Jane'
      store.lastName = 'Smith'
      store.companyName = 'Tech Solutions Inc'
      store.email = '<EMAIL>'
      store.password = 'Password123!'
      store.confirmPassword = 'Password123!'
      store.selectedCountry = 1
      store.selectedCategory = 1
      store.isTerms = true
      store.isCompany = true
      
      // Form should now be valid
      expect(store.isFormValid).toBe(true)
    })
  })

  describe('Profile Creation Store', () => {
    it('should initialize with default values', () => {
      const store = useProfileCreationStore()
      
      expect(store.motivation).toBe('')
      expect(store.phone).toBe('')
      expect(store.city).toBe('')
      expect(store.selectedCountry).toBe(null)
      expect(store.currentRole).toBe('')
      expect(store.company).toBe('')
      expect(store.experienceLevel).toBe('')
      expect(store.aspirations).toBe('')
      expect(store.education).toEqual([])
      expect(store.experience).toEqual([])
      expect(store.skills).toEqual([])
    })

    it('should update fields correctly', () => {
      const store = useProfileCreationStore()
      
      store.motivation = 'I want to help build amazing tech solutions'
      store.phone = '+1234567890'
      store.city = 'Tech City'
      store.currentRole = 'Software Developer'
      store.company = 'Tech Corp'
      
      expect(store.motivation).toBe('I want to help build amazing tech solutions')
      expect(store.phone).toBe('+1234567890')
      expect(store.city).toBe('Tech City')
      expect(store.currentRole).toBe('Software Developer')
      expect(store.company).toBe('Tech Corp')
    })

    it('should add education entry', () => {
      const store = useProfileCreationStore()
      
      const education = {
        institution: 'Tech University',
        degree: 'Computer Science',
        graduationYear: '2023'
      }
      
      store.addEducation(education)
      
      expect(store.education).toHaveLength(1)
      expect(store.education[0]).toEqual(education)
    })

    it('should add experience entry', () => {
      const store = useProfileCreationStore()
      
      const experience = {
        company: 'Previous Tech',
        position: 'Junior Developer',
        duration: '2 years'
      }
      
      store.addExperience(experience)
      
      expect(store.experience).toHaveLength(1)
      expect(store.experience[0]).toEqual(experience)
    })

    it('should add skill', () => {
      const store = useProfileCreationStore()
      
      store.addSkill('JavaScript')
      store.addSkill('Vue.js')
      
      expect(store.skills).toHaveLength(2)
      expect(store.skills).toContain('JavaScript')
      expect(store.skills).toContain('Vue.js')
    })

    it('should remove skill', () => {
      const store = useProfileCreationStore()
      
      store.addSkill('JavaScript')
      store.addSkill('Vue.js')
      store.addSkill('React')
      
      store.removeSkill('Vue.js')
      
      expect(store.skills).toHaveLength(2)
      expect(store.skills).toContain('JavaScript')
      expect(store.skills).toContain('React')
      expect(store.skills).not.toContain('Vue.js')
    })
  })

  describe('Company Profile Creation Store', () => {
    it('should initialize with default values', () => {
      const store = useCompanyProfileCreationStore()
      
      expect(store.company).toBe('')
      expect(store.team).toBe('')
      expect(store.funded).toBe('')
      expect(store.structure).toBe('')
      expect(store.website).toBe('')
      expect(store.description).toBe('')
      expect(store.future_plan_12_18_months).toBe('')
      expect(store.plan).toBe('')
      expect(store.price).toBe('')
      expect(store.duration).toBe('')
      expect(store.card_name).toBe('')
      expect(store.credit_card).toBe('')
      expect(store.expiration_date).toBe('')
      expect(store.security_code).toBe('')
    })

    it('should update fields correctly', () => {
      const store = useCompanyProfileCreationStore()
      
      store.company = 'We are a growing tech startup'
      store.team = '10-50'
      store.funded = 'Seed funded'
      store.structure = 'Private Limited'
      store.website = 'https://techsolutions.com'
      
      expect(store.company).toBe('We are a growing tech startup')
      expect(store.team).toBe('10-50')
      expect(store.funded).toBe('Seed funded')
      expect(store.structure).toBe('Private Limited')
      expect(store.website).toBe('https://techsolutions.com')
    })

    it('should set field using setField method', () => {
      const store = useCompanyProfileCreationStore()
      
      store.setField('company', 'We are a growing tech startup')
      store.setField('team', '10-50')
      
      expect(store.company).toBe('We are a growing tech startup')
      expect(store.team).toBe('10-50')
    })

    it('should validate payment information', () => {
      const store = useCompanyProfileCreationStore()
      
      // Initially payment should be invalid
      expect(store.isPaymentValid).toBe(false)
      
      // Fill payment info
      store.card_name = 'Jane Smith'
      store.credit_card = '****************'
      store.expiration_date = '12/25'
      store.security_code = '123'
      
      // Payment should now be valid
      expect(store.isPaymentValid).toBe(true)
    })

    it('should validate credit card format', () => {
      const store = useCompanyProfileCreationStore()
      
      store.credit_card = '****************'
      expect(store.isValidCreditCard).toBe(true)
      
      store.credit_card = '1234'
      expect(store.isValidCreditCard).toBe(false)
      
      store.credit_card = 'invalid'
      expect(store.isValidCreditCard).toBe(false)
    })
  })
})
