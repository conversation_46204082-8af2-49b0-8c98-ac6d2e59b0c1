import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import Button from '@/ui-kit/Button.vue'

describe('Button Component', () => {
  it('should render with buttonLabel', () => {
    const wrapper = mount(But<PERSON>, {
      props: {
        buttonLabel: 'Login',
        variant: 'primary'
      }
    })

    expect(wrapper.text()).toContain('Login')
  })

  it('should apply variant classes', () => {
    const wrapper = mount(Button, {
      props: {
        buttonLabel: 'Login',
        variant: 'secondary'
      }
    })

    expect(wrapper.classes()).toContain('border-2')
    expect(wrapper.classes()).toContain('border-primary-blue1')
  })

  it('should be disabled when disabled prop is true', () => {
    const wrapper = mount(Button, {
      props: {
        buttonLabel: 'Login',
        variant: 'primary',
        disabled: true
      }
    })

    expect(wrapper.attributes('disabled')).toBeDefined()
  })

  it('should show loading state when loading prop is true', () => {
    const wrapper = mount(But<PERSON>, {
      props: {
        buttonLabel: 'Login',
        variant: 'primary',
        loading: true
      }
    })

    // Check for loading spinner SVG
    expect(wrapper.find('svg.animate-spin').exists()).toBe(true)
    // Check that button text is hidden when loading
    expect(wrapper.text()).not.toContain('Login')
  })

  it('should apply custom classes', () => {
    const wrapper = mount(Button, {
      props: {
        buttonLabel: 'Login',
        variant: 'primary',
        class: 'custom-class'
      }
    })

    expect(wrapper.classes()).toContain('custom-class')
  })

  it('should have data-cy attribute', () => {
    const wrapper = mount(Button, {
      props: {
        buttonLabel: 'Login',
        variant: 'primary',
        dataCy: 'login-button'
      }
    })

    expect(wrapper.attributes('data-cy')).toBe('login-button')
  })

  it('should emit submit event when clicked', async () => {
    const wrapper = mount(Button, {
      props: {
        buttonLabel: 'Login',
        variant: 'primary'
      }
    })

    await wrapper.trigger('click')
    expect(wrapper.emitted('submit')).toBeTruthy()
  })
})
