package com.skilled.SkilledUp;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.testng.Assert;
import org.testng.annotations.Test;

public class TechComLogin {
@Test
	public void RegisterVolUI() {
		WebDriver driver=new ChromeDriver();
	    driver.get("https://v2.skilledup.life/company-register");
	    //Volunteer
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[1]/div[2]/div[1]/div[2]/button[1]")).isDisplayed());
	    //Oppurtunities
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[1]/div[2]/div[1]/div[2]/button[2]")).isDisplayed());
	     //Companies
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[1]/div[2]/div[1]/div[2]/button[3]")).isDisplayed());
	    //POst an oppurtunity
	    //Join as Tech Company 
	    Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/h2")).isDisplayed());
	       //Username
	   		Assert.assertTrue(driver.findElement(By.xpath("//label[contains(text(),'User Name')]")).isDisplayed());
	  		////*[@id="app"]/div/div[2]/div/div/form/div[1]/div[1]/label
	  		//First Name
	  		Assert.assertTrue(driver.findElement(By.xpath("//label[contains(text(),'First Name')]")).isDisplayed());
	  		//Last Name
	  		Assert.assertTrue(driver.findElement(By.xpath("//label[contains(text(),'Last Name')]")).isDisplayed());
	  		//Comapny name
	  		Assert.assertTrue(driver.findElement(By.xpath("//label[contains(text(),'Company Name')]")).isDisplayed());
	  		////*[@id="app"]/div/div[2]/div/div/form/div[3]/div[1]/label
	  		//Email
	  		Assert.assertTrue(driver.findElement(By.xpath("//label[contains(text(),'Email')]")).isDisplayed());
	  		System.out.println("Element Email");
	  		//Password
	  		Assert.assertTrue(driver.findElement(By.xpath("//label[contains(text(),'Password')]")).isDisplayed());
	  		//Confirm Password
	  		Assert.assertTrue(driver.findElement(By.xpath("//label[contains(text(),'Confirm Password')]")).isDisplayed());
	  		//Country
	  		Assert.assertTrue(driver.findElement(By.xpath("//label[contains(text(),'Country')]")).isDisplayed());
	  	        //Category
	  		Assert.assertTrue(driver.findElement(By.xpath("//label[contains(text(),'Category')]")).isDisplayed());
	  		//I confirm I am building a Tech Company
	  		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/div[7]/div[1]/label")).isDisplayed());
	  		//I accept the Terms of Service and privacy Policy
	  		Assert.assertTrue(driver.findElement(By.xpath("//a[contains(text(), 'Terms of Service')]")).isDisplayed());
	  		Assert.assertTrue(driver.findElement(By.xpath("//a[contains(text(), 'Privacy Policy')]")).isDisplayed());
	  		//Create button
	  		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/form/button")).isDisplayed());
	  		//REgister as tech company 
	  		Assert.assertTrue(driver.findElement(By.xpath("//*[@id=\"app\"]/div/div[2]/div/div/div/div/a")).isDisplayed());
            System.out.println("Regsiter button");
}
}
