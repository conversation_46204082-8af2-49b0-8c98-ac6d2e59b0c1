// 🧪 Critical E2E Test: Login Functionality
// Tests the actual SkilledUp Life login flow with proper mocking

import { test, expect } from '@playwright/test'

test.describe('@critical Critical User Flows - Login', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/login')
  })

  test('should render login form with all required elements', async ({ page }) => {
    // Check form elements are present
    await expect(page.getByRole('heading', { name: 'Welcome to SkilledUp Life' })).toBeVisible()
    await expect(page.getByRole('textbox', { name: 'Email' })).toBeVisible()
    await expect(page.getByLabel('Password')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Login' })).toBeVisible()
    await expect(page.getByRole('link', { name: 'Forgot password?' })).toBeVisible()
    await expect(page.getByRole('link', { name: 'Register' })).toBeVisible()
  })

  test('should validate required fields and disable submit button', async ({ page }) => {
    const loginButton = page.getByRole('button', { name: 'Login' })
    
    // Button should be disabled initially (no email/password)
    await expect(loginButton).toBeDisabled()
    
    // Fill only email - button should still be disabled
    await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>')
    await expect(loginButton).toBeDisabled()
    
    // Fill only password - button should still be disabled (invalid email format)
    await page.getByRole('textbox', { name: 'Email' }).clear()
    await page.getByLabel('Password').fill('password123')
    await expect(loginButton).toBeDisabled()
    
    // Fill valid email and password - button should be enabled
    await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>')
    await expect(loginButton).toBeEnabled()
  })

  test('should show validation errors for invalid email format', async ({ page }) => {
    const emailInput = page.getByRole('textbox', { name: 'Email' })
    const passwordInput = page.getByLabel('Password')
    
    // Fill invalid email format
    await emailInput.fill('invalid-email')
    await passwordInput.fill('password123')
    
    // Button should be disabled due to invalid email
    const loginButton = page.getByRole('button', { name: 'Login' })
    await expect(loginButton).toBeDisabled()
  })

  test('should show validation errors for empty password', async ({ page }) => {
    const emailInput = page.getByRole('textbox', { name: 'Email' })
    const passwordInput = page.getByLabel('Password')
    
    // Fill valid email but empty password
    await emailInput.fill('<EMAIL>')
    await passwordInput.clear()
    
    // Button should be disabled due to empty password
    const loginButton = page.getByRole('button', { name: 'Login' })
    await expect(loginButton).toBeDisabled()
  })

  test('volunteer login → dashboard @regression', async ({ page }) => {
    // Mock successful login response for volunteer
    await page.route('**/login', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            token: 'mock-volunteer-token',
            user: { 
              role: ['volunteer'], 
              email_verified: true,
              profile_step: 7, // Complete profile
              name: 'testuser'
            },
            email_verified: true
          }
        })
      })
    })
    
    // Mock profile search response - the actual endpoint is /search with query params
    await page.route('**/search?**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: {
            volunteer: { id: 1, name: 'testuser' }
          }
        })
      })
    })
    
    // Fill and submit form
    await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()
    
    // Should redirect to volunteer dashboard
    await expect(page).toHaveURL('/volunteer/dashboard')
  })

  test('company login → dashboard', async ({ page }) => {
    // Mock successful login response for company
    await page.route('**/login', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            token: 'mock-company-token',
            user: { 
              role: ['company'], 
              email_verified: true,
              profile_step: 3, // Complete profile
              name: 'testcompany'
            },
            email_verified: true
          }
        })
      })
    })
    
    // Mock profile search response
    await page.route('**/search?**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: {
            company: { id: 1, name: 'testcompany' }
          }
        })
      })
    })
    
    // Fill and submit form
    await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()
    
    // Should redirect to company dashboard
    await expect(page).toHaveURL('/company/dashboard')
  })

  test('superadmin login → system dashboard', async ({ page }) => {
    // Mock successful login response for superadmin
    await page.route('**/login', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            token: 'mock-admin-token',
            user: { 
              role: ['superadmin'], 
              email_verified: true,
              name: 'admin'
            },
            email_verified: true
          }
        })
      })
    })
    
    // Fill and submit form
    await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()
    
    // Should redirect to system dashboard
    await expect(page).toHaveURL('/system/dashboard')
  })

  test('redirect to verify email when not verified @regression', async ({ page }) => {
    // Mock login response for unverified user
    await page.route('**/login', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            token: 'mock-token',
            user: { 
              role: ['volunteer'], 
              email_verified: false,
              name: 'testuser'
            },
            email_verified: false
          }
        })
      })
    })
    
    // Fill and submit form
    await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()
    
    // Should redirect to email verification
    await expect(page).toHaveURL('/registration/verify-email')
  })

  test('redirect to profile creation if profile incomplete @regression', async ({ page }) => {
    // Mock login response for incomplete profile
    await page.route('**/login', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            token: 'mock-token',
            user: { 
              role: ['volunteer'], 
              email_verified: true,
              profile_step: 1, // Incomplete profile
              name: 'testuser'
            },
            email_verified: true
          }
        })
      })
    })

    // Mock profile search response (since app may fetch profile after login)
    await page.route('**/search?**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: {
            volunteer: { id: 1, name: 'testuser' }
          }
        })
      })
    })
    
    // Fill and submit form
    await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()
    
    // Should redirect to profile creation
    await expect(page).toHaveURL('/registration/volunteer/profile-creation')
  })

  test('invalid credentials shows error', async ({ page }) => {
    // Mock failed login response
    await page.route('**/login', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: 'Invalid credentials'
        })
      })
    })
    
    // Fill and submit form
    await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>')
    await page.getByLabel('Password').fill('wrongpassword')
    await page.getByRole('button', { name: 'Login' }).click()
    
    // Should show error message
    await expect(page.getByText('Invalid credentials')).toBeVisible()
    
    // Should stay on login page
    await expect(page).toHaveURL('/auth/login')
  })

  test('remember me persists email', async ({ page }) => {
    // Mock successful login
    await page.route('**/login', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            token: 'mock-token',
            user: { 
              role: ['volunteer'], 
              email_verified: true,
              profile_step: 7,
              name: 'testuser'
            },
            email_verified: true
          }
        })
      })
    })
    
    // Mock profile search response
    await page.route('**/search?**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: {
            volunteer: { id: 1, name: 'testuser' }
          }
        })
      })
    })
    
    // Fill form with remember me checked
    await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    // Hidden input: click the label text instead
    await page.getByText('Remember me').click()
    
    // Submit form
    await page.getByRole('button', { name: 'Login' }).click()
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/volunteer/dashboard')
    
    // Verify email is saved to localStorage
    const savedEmail = await page.evaluate(() => localStorage.getItem('authEmail'))
    expect(savedEmail).toBe('<EMAIL>')
  })

  test('opens forgot password modal', async ({ page }) => {
    // Click forgot password link
    await page.getByRole('link', { name: 'Forgot password?' }).click()
    
    // Should open forgot password modal
    await expect(
      page.getByRole('heading', { 
        name: /Enter your email and we'll send you a link to reset your password/i 
      })
    ).toBeVisible()
  })

  test('navigate to registration when register clicked', async ({ page }) => {
    // Click register link
    await page.getByRole('link', { name: 'Register' }).click()
    
    // Should navigate to pre-registration page
    await expect(page).toHaveURL('/registration/pre-registration')
  })
})
