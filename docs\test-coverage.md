# 📊 Test Coverage Dashboard - SkilledUp.Life

> **Live Tracker**: [Google Sheets Version](https://docs.google.com/spreadsheets/d/1q2xA4L0VjMm7GEi-NSbk-X55E4FXBHt9O2PLc0jlqhk/edit?usp=sharing)

---

## 🎯 Current Status (Last Updated: 2025-01-20)

### **Overall Coverage**
- **Unit/Component**: ✅ **Stable** (64/64 tests passing)
- **E2E Critical**: ✅ **Login, Forgot Password** (15/15 tests passing)
- **E2E Regression**: ✅ **Seeded via tags** (nightly job enabled)
- **Integration**: ⏳ **Placeholders added** (*.blank files)

### **Stability Metrics**
- **Unit/Component pass rate**: 100% (last run)
- **E2E pass rate** (Chromium local): 100% (Login 13/13, Forgot Password 2/2)
- **Flaky tests**: None observed after hardening selectors and mocks

---

## 🧪 Unit Tests

| Module | Feature | Test Name | Status | Location | Priority | Owner |
|--------|---------|-----------|--------|----------|----------|-------|
| Authentication | Login | User Login | ✅ Done | `tests/unit/Login.spec.js` | P0 | Frontend QA |
| Registration | Form Validation | Register Form Validation | ✅ Done | `tests/unit/Register.spec.js` | P0 | Frontend QA |
| API | Error Handling | API Error Handling | 🔴 Missing | Backend scope | P2 | Backend QA |

### **Component Tests**
- ✅ `tests/components/Button.spec.js`
- ✅ `tests/components/CheckBox.spec.js`
- ✅ `tests/components/InputField.spec.js`
- ✅ `tests/components/ForgotPassword.spec.js`

---

## 🚀 E2E Tests

| Module | Feature | Test Scenario | Status | Location | Priority | Owner |
|--------|---------|---------------|--------|----------|----------|-------|
| Authentication | Login | User login → dashboard | ✅ Done | `tests/e2e/critical/login.spec.js` | P0 | Frontend QA |
| Authentication | Password Reset | Forgot password flow | ✅ Done | `tests/e2e/forgot-password.spec.js` | P0 | Frontend QA |
| User Journeys | Company Onboarding | Company onboarding journey | 🟡 WIP | `tests/e2e/user-journeys/company-onboarding.spec.js` | P1 | Frontend QA |
| User Journeys | Volunteer Onboarding | Volunteer onboarding journey | 🟡 WIP | `tests/e2e/user-journeys/volunteer-onboarding.spec.js` | P1 | Frontend QA |

---

## 🔄 Critical Flows Status

| Flow | Status | Notes |
|------|--------|-------|
| **Login** (all roles, redirects, errors) | ✅ Done | Mocked `/login` and `/search`; stable selectors |
| **Forgot Password** | ✅ Done | Validates, submits, confirms |
| **Company Onboarding** | 🟡 WIP | Needs end-to-end journey |
| **Volunteer Onboarding** | 🟡 WIP | Needs end-to-end journey |
| **Profile Update Persistence** | 🔴 Missing | Add regression test |
| **Role Switching** | 🔴 Missing | Add user-journey test |
| **Notification Settings** | 🔴 Missing | Add regression test |
| **Session Expiry** | 🔴 Missing | Add regression test |

---

## 🧪 Regression Tests

| Module | Feature | Regression Scenario | Status | Location |
|--------|---------|-------------------|--------|----------|
| Authentication | Login | Post-refactor login flow | ✅ Done | `tests/e2e/critical/login.spec.js` |
| Profiles | Email Update | Persistence after UI overhaul | 🔴 Missing | `tests/e2e/regression/profile-email.spec.js` |
| Notifications | Settings Toggle | Silent mode re-trigger test | 🔴 Missing | `tests/e2e/regression/notification-settings.spec.js` |

---

## 🔗 Integration Tests (Scaffolded)

| Test | Status | Location | Description |
|------|--------|----------|-------------|
| Auth Login + Store + Profile fetch | ⏳ Scaffolded | `tests/integration/auth-login.integration.spec.js.blank` | Login flow with store updates |
| Registration Volunteer → API → Verify Email | ⏳ Scaffolded | `tests/integration/registration-volunteer.integration.spec.js.blank` | Registration flow with API calls |
| Company Profile Creation | ⏳ Scaffolded | `tests/integration/company-profile.integration.spec.js.blank` | Profile creation wizard |

---

## 🎯 Priority Matrix

### **P0 (Critical - Must Have)**
- ✅ Login flow (all roles)
- ✅ Password reset
- 🔴 Payment flows
- 🔴 Profile update persistence

### **P1 (High - Should Have)**
- 🟡 Company onboarding journey
- 🟡 Volunteer onboarding journey
- 🔴 Role switching
- 🔴 Notification settings

### **P2 (Medium - Nice to Have)**
- 🔴 Session expiry handling
- 🔴 Advanced filtering
- 🔴 Search functionality
- 🔴 Social media integration

---

## 🚀 Next Steps

### **Immediate (Next Sprint)**
1. **Complete onboarding journeys** (Company & Volunteer)
2. **Add profile update persistence** regression test
3. **Implement integration tests** (rename *.blank to *.spec.js)

### **Short Term (Next 2 Sprints)**
1. **Add role switching** user journey test
2. **Add notification settings** regression test
3. **Add session expiry** regression test

### **Medium Term (Next Month)**
1. **Add payment flow** E2E tests
2. **Add search functionality** tests
3. **Add advanced filtering** tests

---

## 🔧 CI & Execution

### **Current CI Jobs**
- **PR/Push**: Lint, Unit/Component, E2E @critical (Chromium-only)
- **Nightly**: E2E @regression (all browsers)
- **Manual**: workflow_dispatch with suite = e2e-critical or e2e-regression

### **Test Execution**
```bash
# Local Development
npm run test:unit          # Unit & Component tests
npm run test:e2e           # E2E tests (Chromium only)

# CI/Staging
npx playwright test --grep @critical    # Critical flows only
npx playwright test --grep @regression  # Regression suite
```

---

## 📝 Changelog

| Date | Change Description | Author |
|------|-------------------|--------|
| 2025-01-20 | Initial documentation setup | Jeremiah Agenyi |
| 2025-08-13 | Login unit tests completed - 14 comprehensive tests | Jeremiah Agenyi |
| 2025-08-13 | Registration Form Validation unit tests completed - 16 tests | Jeremiah Agenyi |
| 2025-08-13 | Updated tracker: Login and Registration unit tests ✅ Done | Jeremiah Agenyi |
| 2025-08-13 | Forgot Password E2E completed - 2 tests passing | Jeremiah Agenyi |
| 2025-08-14 | Login E2E hardened - 13 tests passing; added e2e-tests job in CI | Jeremiah Agenyi |

---

## 🎯 Success Metrics

### **Coverage Goals**
- **Unit/Component**: 80%+ coverage (currently ~60%)
- **E2E Critical**: 100% of P0 flows covered (currently 2/4)
- **E2E Regression**: 100% of P1 flows covered (currently 0/4)

### **Quality Goals**
- **Zero flaky tests** in CI
- **< 5 minute** PR feedback loop
- **100% pass rate** on main branch

---

**Maintainer**: Frontend QA Team  
**Last Updated**: 2025-01-20
